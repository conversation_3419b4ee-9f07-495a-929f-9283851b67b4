package swuclient

import (
	"crypto/hmac"
	"crypto/sha1"
	"encoding/binary"
	"fmt"
	"log/slog"
)

// EAPAKAState represents the current state of EAP-AKA authentication
type EAPAKAState int

const (
	EAPAKAStateInit EAPAKAState = iota
	EAPAKAStateChallenge
	EAPAKAStateResponse
	EAPAKAStateSuccess
	EAPAKAStateFailed
)

// EAPAKAAttribute represents an EAP-AKA attribute with Type-Length-Value format
type EAPAKAAttribute struct {
	Type   uint8
	Length uint8 // Length in 4-byte units
	Value  []byte
}

// EAPAKAPacket represents an EAP-AKA packet
type EAPAKAPacket struct {
	Code     uint8  // EAP Code (Request/Response/Success/Failure)
	ID       uint8  // EAP Identifier
	Length   uint16 // EAP Length
	Type     uint8  // EAP Type (23 for AKA)
	Subtype  uint8  // EAP-AKA Subtype
	Reserved uint16 // Reserved field
	Attrs    []EAPAKAAttribute
}

// EAPAKAHandler handles EAP-AKA authentication protocol
type EAPAKAHandler struct {
	logger *slog.Logger
	state  EAPAKAState

	// Authentication parameters
	identity string
	rand     []byte
	autn     []byte
	res      []byte
	ik       []byte
	ck       []byte

	// EAP-AKA keys
	kEncr []byte // Encryption key
	kAut  []byte // Authentication key
	msk   []byte // Master Session Key
	emsk  []byte // Extended Master Session Key
	mk    []byte // Master Key

	// EAP state
	eapID uint8
}

// NewEAPAKAHandler creates a new EAP-AKA authentication handler
func NewEAPAKAHandler(logger *slog.Logger, identity string) *EAPAKAHandler {
	return &EAPAKAHandler{
		logger:   logger,
		state:    EAPAKAStateInit,
		identity: identity,
	}
}

// MarshalAttribute marshals an EAP-AKA attribute to bytes
func (attr *EAPAKAAttribute) MarshalAttribute() ([]byte, error) {
	// Calculate length in 4-byte units (including type and length fields)
	totalLen := len(attr.Value) + 2
	if totalLen%4 != 0 {
		// Add padding to align to 4-byte boundary
		padding := 4 - (totalLen % 4)
		attr.Value = append(attr.Value, make([]byte, padding)...)
		totalLen += padding
	}

	attr.Length = uint8(totalLen / 4)

	buf := make([]byte, totalLen)
	buf[0] = attr.Type
	buf[1] = attr.Length
	copy(buf[2:], attr.Value)

	return buf, nil
}

// UnmarshalAttributes unmarshals EAP-AKA attributes from bytes
func UnmarshalAttributes(data []byte) ([]EAPAKAAttribute, error) {
	var attrs []EAPAKAAttribute
	offset := 0

	for offset < len(data) {
		if offset+2 > len(data) {
			return nil, fmt.Errorf("insufficient data for attribute header")
		}

		attr := EAPAKAAttribute{
			Type:   data[offset],
			Length: data[offset+1],
		}

		// Length is in 4-byte units, minimum is 1 (for type+length only)
		if attr.Length == 0 {
			return nil, fmt.Errorf("invalid attribute length: 0")
		}

		totalLen := int(attr.Length) * 4
		if offset+totalLen > len(data) {
			return nil, fmt.Errorf("attribute length exceeds data bounds")
		}

		// Extract value (excluding type and length bytes)
		valueLen := totalLen - 2
		if valueLen > 0 {
			attr.Value = make([]byte, valueLen)
			copy(attr.Value, data[offset+2:offset+totalLen])
		}

		attrs = append(attrs, attr)
		offset += totalLen
	}

	return attrs, nil
}

// MarshalPacket marshals an EAP-AKA packet to bytes
func (pkt *EAPAKAPacket) MarshalPacket() ([]byte, error) {
	// Calculate total length
	totalLen := 8 // EAP header (4) + Type (1) + Subtype (1) + Reserved (2)

	// Marshal attributes first to get their total length
	var attrData []byte
	for _, attr := range pkt.Attrs {
		attrBytes, err := attr.MarshalAttribute()
		if err != nil {
			return nil, fmt.Errorf("failed to marshal attribute: %w", err)
		}
		attrData = append(attrData, attrBytes...)
	}

	totalLen += len(attrData)
	pkt.Length = uint16(totalLen)

	buf := make([]byte, totalLen)

	// EAP header
	buf[0] = pkt.Code
	buf[1] = pkt.ID
	binary.BigEndian.PutUint16(buf[2:4], pkt.Length)

	// EAP-AKA specific fields
	buf[4] = pkt.Type
	buf[5] = pkt.Subtype
	binary.BigEndian.PutUint16(buf[6:8], pkt.Reserved)

	// Attributes
	copy(buf[8:], attrData)

	return buf, nil
}

// UnmarshalPacket unmarshals an EAP-AKA packet from bytes
func UnmarshalPacket(data []byte) (*EAPAKAPacket, error) {
	if len(data) < 8 {
		return nil, fmt.Errorf("insufficient data for EAP-AKA packet header")
	}

	pkt := &EAPAKAPacket{
		Code:     data[0],
		ID:       data[1],
		Length:   binary.BigEndian.Uint16(data[2:4]),
		Type:     data[4],
		Subtype:  data[5],
		Reserved: binary.BigEndian.Uint16(data[6:8]),
	}

	// Validate length
	if int(pkt.Length) != len(data) {
		return nil, fmt.Errorf("packet length mismatch: header says %d, got %d", pkt.Length, len(data))
	}

	// Parse attributes if present
	if len(data) > 8 {
		attrs, err := UnmarshalAttributes(data[8:])
		if err != nil {
			return nil, fmt.Errorf("failed to unmarshal attributes: %w", err)
		}
		pkt.Attrs = attrs
	}

	return pkt, nil
}

// ProcessChallenge processes an EAP-AKA Challenge request
func (h *EAPAKAHandler) ProcessChallenge(challengePkt *EAPAKAPacket, usimAuth func([]byte, []byte) ([]byte, []byte, []byte, error)) (*EAPAKAPacket, error) {
	h.logger.Debug("[SWu Client] processing EAP-AKA Challenge", "packet_id", challengePkt.ID)

	// Extract RAND and AUTN from challenge
	var rand, autn, mac []byte
	for _, attr := range challengePkt.Attrs {
		switch attr.Type {
		case ATRand:
			if len(attr.Value) >= 16 {
				rand = attr.Value[2:18] // Skip 2-byte reserved field
			}
		case ATAutn:
			if len(attr.Value) >= 16 {
				autn = attr.Value[2:18] // Skip 2-byte reserved field
			}
		case ATMac:
			if len(attr.Value) >= 16 {
				mac = attr.Value[:16]
			}
		}
	}

	if len(rand) != 16 || len(autn) != 16 {
		return nil, fmt.Errorf("invalid RAND or AUTN in challenge")
	}

	h.logger.Debug("[SWu Client] EAP-AKA Challenge parameters",
		"rand", fmt.Sprintf("%x", rand),
		"autn", fmt.Sprintf("%x", autn),
		"mac", fmt.Sprintf("%x", mac))

	// Perform USIM authentication
	res, ik, ck, err := usimAuth(rand, autn)
	if err != nil {
		h.logger.Error("[SWu Client] USIM authentication failed", "error", err)
		return nil, fmt.Errorf("USIM authentication failed: %w", err)
	}

	h.logger.Debug("[SWu Client] USIM authentication successful",
		"res", fmt.Sprintf("%x", res),
		"ik", fmt.Sprintf("%x", ik),
		"ck", fmt.Sprintf("%x", ck))

	// Store authentication results
	h.rand = rand
	h.autn = autn
	h.res = res
	h.ik = ik
	h.ck = ck
	h.eapID = challengePkt.ID

	// Generate EAP-AKA keys
	if err := h.generateKeys(); err != nil {
		return nil, fmt.Errorf("failed to generate EAP-AKA keys: %w", err)
	}

	// Create response packet
	responsePkt := &EAPAKAPacket{
		Code:     EAPCodeResponse,
		ID:       challengePkt.ID,
		Type:     EAPTypeAKA,
		Subtype:  EAPAKASubtypeResponse,
		Reserved: 0,
	}

	// Add RES attribute
	resAttr := EAPAKAAttribute{
		Type:  ATRes,
		Value: append([]byte{0, uint8(len(res))}, res...), // Length prefix + RES
	}

	// Add MAC attribute (placeholder for now, will be calculated)
	macAttr := EAPAKAAttribute{
		Type:  ATMac,
		Value: make([]byte, 16), // Will be filled with actual MAC
	}

	responsePkt.Attrs = []EAPAKAAttribute{resAttr, macAttr}

	// Calculate and set MAC
	if err := h.calculateMAC(responsePkt); err != nil {
		return nil, fmt.Errorf("failed to calculate MAC: %w", err)
	}

	h.state = EAPAKAStateResponse
	h.logger.Debug("[SWu Client] EAP-AKA Challenge response created")

	return responsePkt, nil
}

// generateKeys generates EAP-AKA keying material following RFC 4187
func (h *EAPAKAHandler) generateKeys() error {
	// Generate Master Key (MK) = SHA1(Identity | IK | CK)
	mkInput := []byte(h.identity)
	mkInput = append(mkInput, h.ik...)
	mkInput = append(mkInput, h.ck...)

	hash := sha1.New()
	hash.Write(mkInput)
	h.mk = hash.Sum(nil)

	h.logger.Debug("[SWu Client] EAP-AKA Master Key generated",
		"mk", fmt.Sprintf("%x", h.mk))

	// Generate key material using FIPS 186-2 + change notice 1
	// This generates 160 bytes of key material
	keyMaterial := h.generateKeyMaterial(h.mk, 160)

	// Extract keys from key material
	h.kEncr = keyMaterial[0:16]  // K_encr (128 bits)
	h.kAut = keyMaterial[16:32]  // K_aut (128 bits)
	h.msk = keyMaterial[32:96]   // MSK (512 bits)
	h.emsk = keyMaterial[96:160] // EMSK (512 bits)

	h.logger.Debug("[SWu Client] EAP-AKA keys derived",
		"k_encr", fmt.Sprintf("%x", h.kEncr),
		"k_aut", fmt.Sprintf("%x", h.kAut),
		"msk", fmt.Sprintf("%x", h.msk[:16]), // Log first 16 bytes only
		"emsk", fmt.Sprintf("%x", h.emsk[:16])) // Log first 16 bytes only

	return nil
}

// generateKeyMaterial generates key material using FIPS 186-2 + change notice 1
func (h *EAPAKAHandler) generateKeyMaterial(seed []byte, length int) []byte {
	result := make([]byte, 0, length)
	xval := make([]byte, len(seed))
	copy(xval, seed)

	// 2^160 modulus for SHA-1
	modulus := make([]byte, 21)
	modulus[0] = 1 // 2^160

	for len(result) < length {
		// w0 = SHA1(xval)
		hash := sha1.New()
		hash.Write(xval)
		w0 := hash.Sum(nil)

		// xval = (xval + w0 + 1) mod 2^160
		xval = h.addMod(xval, w0, modulus)

		// w1 = SHA1(xval)
		hash = sha1.New()
		hash.Write(xval)
		w1 := hash.Sum(nil)

		// xval = (xval + w1 + 1) mod 2^160
		xval = h.addMod(xval, w1, modulus)

		// Append w0 || w1
		result = append(result, w0...)
		result = append(result, w1...)
	}

	return result[:length]
}

// addMod performs (a + b + 1) mod m for big integers represented as byte arrays
func (h *EAPAKAHandler) addMod(a, b, m []byte) []byte {
	// Simple implementation - for production use math/big
	// This is a simplified version for the specific case of 2^160
	result := make([]byte, 20) // SHA-1 output size
	carry := uint16(1)         // +1 part

	// Add from right to left
	for i := 19; i >= 0; i-- {
		sum := carry
		if i < len(a) {
			sum += uint16(a[i])
		}
		if i < len(b) {
			sum += uint16(b[i])
		}
		result[i] = uint8(sum & 0xFF)
		carry = sum >> 8
	}

	return result
}

// calculateMAC calculates and sets the MAC attribute for EAP-AKA packet
func (h *EAPAKAHandler) calculateMAC(pkt *EAPAKAPacket) error {
	// Find MAC attribute and zero it out for calculation
	var macAttrIndex int = -1
	for i, attr := range pkt.Attrs {
		if attr.Type == ATMac {
			macAttrIndex = i
			// Zero out MAC value for calculation
			pkt.Attrs[i].Value = make([]byte, 16)
			break
		}
	}

	if macAttrIndex == -1 {
		return fmt.Errorf("MAC attribute not found")
	}

	// Marshal packet for MAC calculation
	pktBytes, err := pkt.MarshalPacket()
	if err != nil {
		return fmt.Errorf("failed to marshal packet for MAC calculation: %w", err)
	}

	// Calculate HMAC-SHA1 using K_aut
	mac := hmac.New(sha1.New, h.kAut)
	mac.Write(pktBytes)
	macValue := mac.Sum(nil)

	// Set MAC value (first 16 bytes of HMAC-SHA1)
	pkt.Attrs[macAttrIndex].Value = macValue[:16]

	h.logger.Debug("[SWu Client] EAP-AKA MAC calculated",
		"mac", fmt.Sprintf("%x", macValue[:16]))

	return nil
}

// GetMSK returns the Master Session Key
func (h *EAPAKAHandler) GetMSK() []byte {
	return h.msk
}

// GetState returns the current EAP-AKA state
func (h *EAPAKAHandler) GetState() EAPAKAState {
	return h.state
}

// SetState sets the EAP-AKA state
func (h *EAPAKAHandler) SetState(state EAPAKAState) {
	h.state = state
	h.logger.Debug("[SWu Client] EAP-AKA state changed", "state", state)
}
