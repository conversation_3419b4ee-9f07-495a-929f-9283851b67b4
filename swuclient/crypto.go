package swuclient

import (
	"crypto/hmac"
	"crypto/md5"
	"crypto/rand"
	"crypto/sha1"
	"crypto/sha256"
	"fmt"
	"hash"
	"log/slog"
	"math/big"
)

// Crypto algorithm constants
const (
	// Encryption algorithms
	EncryptionAESCBC = 12

	// Integrity algorithms
	AuthHMACMD596      = 1
	AuthHMACSHA196     = 2
	AuthHMACSHA2256128 = 12

	// PRF algorithms
	PRFHMACMD5     = 1
	PRFHMACSHA1    = 2
	PRFHMACSHA2256 = 5

	// DH Groups
	DHGroup1024MODP = 2
)

// DH Group 2 (1024-bit MODP) parameters - RFC 2409
var (
	DHGroup2Prime     = mustParseBigInt("FFFFFFFFFFFFFFFFC90FDAA22168C234C4C6628B80DC1CD129024E088A67CC74020BBEA63B139B22514A08798E3404DDEF9519B3CD3A431B302B0A6DF25F14374FE1356D6D51C245E485B576625E7EC6F44C42E9A637ED6B0BFF5CB6F406B7EDEE386BFB5A899FA5AE9F24117C4B1FE649286651ECE65381FFFFFFFFFFFFFFFF")
	DHGroup2Generator = big.NewInt(2)
)

func mustParseBigInt(s string) *big.Int {
	n, ok := new(big.Int).SetString(s, 16)
	if !ok {
		panic("failed to parse big int: " + s)
	}
	return n
}

type Crypto struct {
	logger *slog.Logger

	// DH parameters
	DHGroup      uint16
	DHPrivateKey *big.Int
	DHPublicKey  *big.Int
	DHSharedKey  []byte

	// Crypto parameters
	prfAlgo          uint16
	integAlgo        uint16
	encryptionAlgo   uint16
	encryptionKeyLen uint16

	// SPI values
	spiInitiator uint64
	spiResponder uint64

	// Nonces
	nonceInitiator []byte
	nonceResponder []byte

	// Derived keys
	skeyseed []byte
	skd      []byte
	skAi     []byte
	skAr     []byte
	skEi     []byte
	skEr     []byte
	skPi     []byte
	skPr     []byte

	// EAP-AKA keys
	eapMSK []byte
}

func NewCrypto(logger *slog.Logger) (*Crypto, error) {
	c := Crypto{
		logger: logger,
	}
	if err := c.initialize(); err != nil {
		return nil, err
	}
	return &c, nil
}

func (c *Crypto) initialize() error {
	privateKey, err := rand.Int(rand.Reader, DHGroup2Prime)
	if err != nil {
		return fmt.Errorf("unable to generate DH private key: %w", err)
	}
	c.DHPrivateKey = privateKey
	c.DHPublicKey = new(big.Int).Exp(DHGroup2Generator, c.DHPrivateKey, DHGroup2Prime)
	c.logger.Debug("[SWu Client] DH key pair generated", "group", DHGroup2Prime)
	return nil
}

func (c *Crypto) GetPublicKey() []byte {
	bs := c.DHPublicKey.Bytes()
	padded := make([]byte, 128)
	if len(bs) <= 128 {
		copy(padded[128-len(bs):], bs)
		bs = padded
	} else {
		bs = bs[len(bs)-128:]
	}
	return bs
}

func (c *Crypto) GenerateIKESecret(pubKey []byte) error {
	// Convert responder's public key to big.Int
	pubKeyBigInt := new(big.Int).SetBytes(pubKey)
	// Calculate shared secret: responder_public^private mod p
	sharedSecret := new(big.Int).Exp(pubKeyBigInt, c.DHPrivateKey, DHGroup2Prime).Bytes()
	// Pad to 128 bytes for 1024-bit group (1024 bits = 128 bytes)
	c.DHSharedKey = make([]byte, 128)
	if len(sharedSecret) <= 128 {
		copy(c.DHSharedKey[128-len(sharedSecret):], sharedSecret)
	} else {
		// If key is longer than expected, truncate to 128 bytes
		copy(c.DHSharedKey, sharedSecret[len(sharedSecret)-128:])
	}
	c.logger.Debug("[SWu Client] DH shared secret generated",
		"length", len(c.DHSharedKey),
		"shared_key_first", fmt.Sprintf("%x", c.DHSharedKey[:min(32, len(c.DHSharedKey))]),
		"private_key_bits", c.DHPrivateKey.BitLen(),
		"public_key_bits", c.DHPublicKey.BitLen())
	return nil
}

func (c *Crypto) SetCryptoMode(prf, integ, encryption, encryptionKeyLen uint16) {
	c.prfAlgo = prf
	c.integAlgo = integ
	c.encryptionAlgo = encryption
	c.encryptionKeyLen = encryptionKeyLen

	c.logger.Debug("[SWu Client] crypto mode set",
		"prf", prf,
		"integ", integ,
		"encryption", encryption,
		"encryption_keylen", encryptionKeyLen)
}

func (c *Crypto) prf(key, data []byte) []byte {
	var h hash.Hash
	switch c.prfAlgo {
	case PRFHMACMD5:
		h = hmac.New(md5.New, key)
	case PRFHMACSHA1:
		h = hmac.New(sha1.New, key)
	case PRFHMACSHA2256:
		h = hmac.New(sha256.New, key)
	default:
		c.logger.Warn("[SWu Client] unknown PRF algorithm", "algorithm", c.prfAlgo)
		return nil
	}
	h.Write(data)
	return h.Sum(nil)
}

func (c *Crypto) prfPlus(key []byte, seed []byte, length int) []byte {
	var result []byte
	var lastSegment []byte
	counter := byte(1)
	for len(result) < length {
		data := append(lastSegment, seed...)
		data = append(data, counter)
		t := c.prf(key, data)
		result = append(result, t...)
		lastSegment = t
		counter++
	}
	return result[:length]
}
