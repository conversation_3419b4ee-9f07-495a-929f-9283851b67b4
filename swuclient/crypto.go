package swuclient

import (
	"crypto/hmac"
	"crypto/md5"
	"crypto/rand"
	"crypto/sha1"
	"crypto/sha256"
	"fmt"
	"hash"
	"log/slog"
	"math/big"
)

// Legacy crypto algorithm constants - use constants.go for new code
const (
	// Encryption algorithms - use EncrAES_CBC from constants.go
	EncryptionAESCBC = EncrAES_CBC

	// Integrity algorithms - use IntegHMACMD596 etc. from constants.go
	AuthHMACMD596      = IntegHMACMD596
	AuthHMACSHA196     = IntegHMACSHA196
	AuthHMACSHA2256128 = IntegHMACSHA2256128

	// DH Groups - use DHGroupMODP1024 from constants.go
	DHGroup1024MODP = DHGroupMODP1024
)

// DH Group 2 (1024-bit MODP) parameters - RFC 2409
var (
	DHGroup2Prime     = mustParseBigInt("FFFFFFFFFFFFFFFFC90FDAA22168C234C4C6628B80DC1CD129024E088A67CC74020BBEA63B139B22514A08798E3404DDEF9519B3CD3A431B302B0A6DF25F14374FE1356D6D51C245E485B576625E7EC6F44C42E9A637ED6B0BFF5CB6F406B7EDEE386BFB5A899FA5AE9F24117C4B1FE649286651ECE65381FFFFFFFFFFFFFFFF")
	DHGroup2Generator = big.NewInt(2)
)

func mustParseBigInt(s string) *big.Int {
	n, ok := new(big.Int).SetString(s, 16)
	if !ok {
		panic("failed to parse big int: " + s)
	}
	return n
}

type Crypto struct {
	logger *slog.Logger

	// DH parameters
	DHGroup      uint16
	DHPrivateKey *big.Int
	DHPublicKey  *big.Int
	DHSharedKey  []byte

	// Crypto parameters
	prfAlg     uint16
	integAlg   uint16
	encrAlg    uint16
	encrKeyLen uint16

	// SPI values
	spiInitiator uint64
	spiResponder uint64

	// Nonces
	nonceInitiator []byte
	nonceResponder []byte

	// Derived keys
	skeyseed []byte
	skd      []byte
	skAi     []byte
	skAr     []byte
	skEi     []byte
	skEr     []byte
	skPi     []byte
	skPr     []byte

	// EAP-AKA keys
	eapMSK []byte
}

func NewCrypto(logger *slog.Logger) (*Crypto, error) {
	c := Crypto{
		logger: logger,
	}
	if err := c.initialize(); err != nil {
		return nil, err
	}
	return &c, nil
}

func (c *Crypto) initialize() error {
	privateKey, err := rand.Int(rand.Reader, DHGroup2Prime)
	if err != nil {
		return fmt.Errorf("unable to generate DH private key: %w", err)
	}
	c.DHPrivateKey = privateKey
	c.DHPublicKey = new(big.Int).Exp(DHGroup2Generator, c.DHPrivateKey, DHGroup2Prime)
	c.logger.Debug("[SWu Client] DH key pair generated", "group", DHGroup2Prime)
	return nil
}

func (c *Crypto) GetPublicKey() []byte {
	bs := c.DHPublicKey.Bytes()
	padded := make([]byte, 128)
	if len(bs) <= 128 {
		copy(padded[128-len(bs):], bs)
		bs = padded
	} else {
		bs = bs[len(bs)-128:]
	}
	return bs
}

func (c *Crypto) GenerateIKESecret(pubKey []byte) error {
	// Convert responder's public key to big.Int
	pubKeyBigInt := new(big.Int).SetBytes(pubKey)
	// Calculate shared secret: responder_public^private mod p
	sharedSecret := new(big.Int).Exp(pubKeyBigInt, c.DHPrivateKey, DHGroup2Prime).Bytes()
	// Pad to 128 bytes for 1024-bit group (1024 bits = 128 bytes)
	c.DHSharedKey = make([]byte, 128)
	if len(sharedSecret) <= 128 {
		copy(c.DHSharedKey[128-len(sharedSecret):], sharedSecret)
	} else {
		// If key is longer than expected, truncate to 128 bytes
		copy(c.DHSharedKey, sharedSecret[len(sharedSecret)-128:])
	}
	c.logger.Debug("[SWu Client] DH shared secret generated",
		"length", len(c.DHSharedKey),
		"shared_key_first", fmt.Sprintf("%x", c.DHSharedKey[:min(32, len(c.DHSharedKey))]),
		"private_key_bits", c.DHPrivateKey.BitLen(),
		"public_key_bits", c.DHPublicKey.BitLen())
	return nil
}

func (c *Crypto) SetCryptoMode(prf, integ, encr, encrKeyLen uint16) {
	c.prfAlg = prf
	c.integAlg = integ
	c.encrAlg = encr
	c.encrKeyLen = encrKeyLen

	c.logger.Debug("[SWu Client] crypto mode set",
		"prf", prf,
		"integ", integ,
		"encryption", encr,
		"encryption_keylen", encrKeyLen)
}

func (c *Crypto) prf(key, data []byte) []byte {
	var h hash.Hash
	switch c.prfAlg {
	case PRFHMACMD5:
		h = hmac.New(md5.New, key)
	case PRFHMACSHA1:
		h = hmac.New(sha1.New, key)
	case PRFHMACSHA256:
		h = hmac.New(sha256.New, key)
	default:
		c.logger.Warn("[SWu Client] unknown PRF algorithm", "algorithm", c.prfAlg)
		return nil
	}
	h.Write(data)
	return h.Sum(nil)
}

func (c *Crypto) prfPlus(key []byte, seed []byte, length int) []byte {
	var result []byte
	var lastSegment []byte
	counter := byte(1)
	for len(result) < length {
		data := append(lastSegment, seed...)
		data = append(data, counter)
		t := c.prf(key, data)
		result = append(result, t...)
		lastSegment = t
		counter++
	}
	return result[:length]
}

// SetNonces sets the initiator and responder nonces
func (c *Crypto) SetNonces(initiator, responder []byte) {
	c.nonceInitiator = make([]byte, len(initiator))
	copy(c.nonceInitiator, initiator)
	c.nonceResponder = make([]byte, len(responder))
	copy(c.nonceResponder, responder)
	c.logger.Debug("[SWu Client] nonces set",
		"initiator_len", len(initiator),
		"responder_len", len(responder))
}

// SetSPIs sets the initiator and responder SPIs
func (c *Crypto) SetSPIs(initiator, responder uint64) {
	c.spiInitiator = initiator
	c.spiResponder = responder
	c.logger.Debug("[SWu Client] SPIs set",
		"initiator", fmt.Sprintf("0x%016x", initiator),
		"responder", fmt.Sprintf("0x%016x", responder))
}

// GenerateIKEKeys generates all IKE keying material from DH shared secret and nonces
func (c *Crypto) GenerateIKEKeys() error {
	if c.DHSharedKey == nil {
		return fmt.Errorf("DH shared key not generated")
	}
	if c.nonceInitiator == nil || c.nonceResponder == nil {
		return fmt.Errorf("nonces not set")
	}

	// Generate SKEYSEED = prf(Ni | Nr, g^ir)
	nonces := append(c.nonceInitiator, c.nonceResponder...)
	c.skeyseed = c.prf(nonces, c.DHSharedKey)
	c.logger.Debug("[SWu Client] SKEYSEED generated",
		"length", len(c.skeyseed),
		"skeyseed", fmt.Sprintf("%x", c.skeyseed[:min(16, len(c.skeyseed))]))

	// Generate key stream: prf+(SKEYSEED, Ni | Nr | SPIi | SPIr)
	stream := make([]byte, 0, len(nonces)+16)
	stream = append(stream, nonces...)

	// Add SPIs in big-endian format
	spiBytes := make([]byte, 16)
	for i := 0; i < 8; i++ {
		spiBytes[i] = byte(c.spiInitiator >> (8 * (7 - i)))
		spiBytes[8+i] = byte(c.spiResponder >> (8 * (7 - i)))
	}
	stream = append(stream, spiBytes...)

	// Calculate total key length needed
	prfLen := c.getPRFLength()
	integLen := c.getIntegKeyLength()
	encrLen := c.getEncrKeyLength()
	totalKeyLen := 3*prfLen + 2*integLen + 2*encrLen

	c.logger.Debug("[SWu Client] key derivation parameters",
		"prf_len", prfLen,
		"integ_len", integLen,
		"encr_len", encrLen,
		"total_key_len", totalKeyLen)

	// Generate key stream
	keyStream := c.prfPlus(c.skeyseed, stream, totalKeyLen)

	// Extract keys from key stream
	offset := 0
	c.skd = keyStream[offset : offset+prfLen]
	offset += prfLen
	c.skAi = keyStream[offset : offset+integLen]
	offset += integLen
	c.skAr = keyStream[offset : offset+integLen]
	offset += integLen
	c.skEi = keyStream[offset : offset+encrLen]
	offset += encrLen
	c.skEr = keyStream[offset : offset+encrLen]
	offset += encrLen
	c.skPi = keyStream[offset : offset+prfLen]
	offset += prfLen
	c.skPr = keyStream[offset : offset+prfLen]

	c.logger.Debug("[SWu Client] IKE keys derived",
		"sk_d", fmt.Sprintf("%x", c.skd[:min(16, len(c.skd))]),
		"sk_ai", fmt.Sprintf("%x", c.skAi[:min(16, len(c.skAi))]),
		"sk_ar", fmt.Sprintf("%x", c.skAr[:min(16, len(c.skAr))]),
		"sk_ei", fmt.Sprintf("%x", c.skEi[:min(16, len(c.skEi))]),
		"sk_er", fmt.Sprintf("%x", c.skEr[:min(16, len(c.skEr))]),
		"sk_pi", fmt.Sprintf("%x", c.skPi[:min(16, len(c.skPi))]),
		"sk_pr", fmt.Sprintf("%x", c.skPr[:min(16, len(c.skPr))]))

	return nil
}

// getPRFLength returns the output length of the PRF function
func (c *Crypto) getPRFLength() int {
	switch c.prfAlg {
	case PRFHMACMD5:
		return 16
	case PRFHMACSHA1:
		return 20
	case PRFHMACSHA256:
		return 32
	case PRFHMACSHA384:
		return 48
	case PRFHMACSHA512:
		return 64
	default:
		c.logger.Warn("[SWu Client] unknown PRF algorithm for length calculation", "algorithm", c.prfAlg)
		return 20 // Default to SHA1 length
	}
}

// getIntegKeyLength returns the key length for the integrity algorithm
func (c *Crypto) getIntegKeyLength() int {
	switch c.integAlg {
	case IntegHMACMD596:
		return 16
	case IntegHMACSHA196:
		return 20
	case IntegHMACSHA2256128:
		return 32
	default:
		c.logger.Warn("[SWu Client] unknown integrity algorithm for key length", "algorithm", c.integAlg)
		return 20 // Default to SHA1 length
	}
}

// getEncrKeyLength returns the key length for the encryption algorithm
func (c *Crypto) getEncrKeyLength() int {
	switch c.encrAlg {
	case EncrAES_CBC:
		return int(c.encrKeyLen / 8) // Convert bits to bytes
	case Encr3DES:
		return 24
	case EncrNULL:
		return 0
	default:
		c.logger.Warn("[SWu Client] unknown encryption algorithm for key length", "algorithm", c.encrAlg)
		return 16 // Default to 128-bit key
	}
}

// GenerateEAPMSK generates the EAP-AKA Master Session Key (MSK)
// MSK = SHA1(Identity | IK | CK) where:
// - Identity is the EAP identity (IMSI)
// - IK is the Integrity Key from USIM authentication
// - CK is the Cipher Key from USIM authentication
func (c *Crypto) GenerateEAPMSK(identity string, ik, ck []byte) error {
	if len(ik) != 16 {
		return fmt.Errorf("invalid IK length: expected 16, got %d", len(ik))
	}
	if len(ck) != 16 {
		return fmt.Errorf("invalid CK length: expected 16, got %d", len(ck))
	}

	// Construct MSK input: Identity | IK | CK
	mskInput := make([]byte, 0, len(identity)+32)
	mskInput = append(mskInput, []byte(identity)...)
	mskInput = append(mskInput, ik...)
	mskInput = append(mskInput, ck...)

	// Generate MSK using SHA1
	hash := sha1.New()
	hash.Write(mskInput)
	c.eapMSK = hash.Sum(nil)

	c.logger.Debug("[SWu Client] EAP-AKA MSK generated",
		"identity", identity,
		"ik", fmt.Sprintf("%x", ik),
		"ck", fmt.Sprintf("%x", ck),
		"msk", fmt.Sprintf("%x", c.eapMSK))

	return nil
}

// GetSKEYSEED returns the generated SKEYSEED
func (c *Crypto) GetSKEYSEED() []byte {
	return c.skeyseed
}

// GetSKd returns the SK_d key
func (c *Crypto) GetSKd() []byte {
	return c.skd
}

// GetSKai returns the SK_ai key (initiator authentication)
func (c *Crypto) GetSKai() []byte {
	return c.skAi
}

// GetSKar returns the SK_ar key (responder authentication)
func (c *Crypto) GetSKar() []byte {
	return c.skAr
}

// GetSKei returns the SK_ei key (initiator encryption)
func (c *Crypto) GetSKei() []byte {
	return c.skEi
}

// GetSKer returns the SK_er key (responder encryption)
func (c *Crypto) GetSKer() []byte {
	return c.skEr
}

// GetSKpi returns the SK_pi key (initiator payload protection)
func (c *Crypto) GetSKpi() []byte {
	return c.skPi
}

// GetSKpr returns the SK_pr key (responder payload protection)
func (c *Crypto) GetSKpr() []byte {
	return c.skPr
}

// GetEAPMSK returns the EAP-AKA Master Session Key
func (c *Crypto) GetEAPMSK() []byte {
	return c.eapMSK
}

// min returns the minimum of two integers
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
