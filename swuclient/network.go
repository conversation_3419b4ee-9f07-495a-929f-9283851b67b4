package swuclient

import (
	"fmt"
	"log/slog"
	"net"
	"time"
)

// NetworkHandler handles UDP communication for IKEv2
type NetworkHandler struct {
	logger *slog.Logger

	// Connection details
	localAddr  *net.UDPAddr
	remoteAddr *net.UDPAddr
	conn       *net.UDPConn

	// NAT traversal
	natTraversal bool
	natPort      int

	// Timeouts
	readTimeout  time.Duration
	writeTimeout time.Duration

	// Non-ESP marker for NAT traversal
	nonESPMarker []byte
}

// NewNetworkHandler creates a new network handler
func NewNetworkHandler(logger *slog.Logger) *NetworkHandler {
	return &NetworkHandler{
		logger:       logger,
		natPort:      IKENATTraversalPort,
		readTimeout:  30 * time.Second,
		writeTimeout: 10 * time.Second,
		nonESPMarker: []byte{0x00, 0x00, 0x00, 0x00}, // Non-ESP marker
	}
}

// Connect establishes UDP connection to ePDG
func (n *NetworkHandler) Connect(epdgAddr string, localPort int) error {
	// Resolve remote address
	remoteAddr, err := net.ResolveUDPAddr("udp", fmt.Sprintf("%s:%d", epdgAddr, IKEPort))
	if err != nil {
		return fmt.Errorf("failed to resolve ePDG address: %w", err)
	}
	n.remoteAddr = remoteAddr

	// Setup local address
	localAddr, err := net.ResolveUDPAddr("udp", fmt.Sprintf(":%d", localPort))
	if err != nil {
		return fmt.Errorf("failed to resolve local address: %w", err)
	}
	n.localAddr = localAddr

	// Create UDP connection
	conn, err := net.DialUDP("udp", localAddr, remoteAddr)
	if err != nil {
		return fmt.Errorf("failed to create UDP connection: %w", err)
	}
	n.conn = conn

	n.logger.Info("[SWu Client] UDP connection established",
		"local", n.localAddr.String(),
		"remote", n.remoteAddr.String())

	return nil
}

// EnableNATTraversal switches to NAT traversal mode (port 4500)
func (n *NetworkHandler) EnableNATTraversal() error {
	if n.conn != nil {
		n.conn.Close()
	}

	// Switch to NAT traversal port
	remoteAddr, err := net.ResolveUDPAddr("udp", fmt.Sprintf("%s:%d", n.remoteAddr.IP.String(), n.natPort))
	if err != nil {
		return fmt.Errorf("failed to resolve NAT traversal address: %w", err)
	}
	n.remoteAddr = remoteAddr

	// Create new connection on NAT traversal port
	conn, err := net.DialUDP("udp", nil, remoteAddr)
	if err != nil {
		return fmt.Errorf("failed to create NAT traversal connection: %w", err)
	}
	n.conn = conn
	n.natTraversal = true

	n.logger.Info("[SWu Client] NAT traversal enabled",
		"remote", n.remoteAddr.String())

	return nil
}

// SendPacket sends an IKE packet over UDP
func (n *NetworkHandler) SendPacket(data []byte) error {
	if n.conn == nil {
		return fmt.Errorf("connection not established")
	}

	// Set write timeout
	if err := n.conn.SetWriteDeadline(time.Now().Add(n.writeTimeout)); err != nil {
		return fmt.Errorf("failed to set write deadline: %w", err)
	}

	var packet []byte

	// Add Non-ESP marker for NAT traversal
	if n.natTraversal {
		packet = append(packet, n.nonESPMarker...)
	}
	packet = append(packet, data...)

	// Send packet
	bytesWritten, err := n.conn.Write(packet)
	if err != nil {
		return fmt.Errorf("failed to send packet: %w", err)
	}

	n.logger.Debug("[SWu Client] packet sent",
		"bytes", bytesWritten,
		"nat_traversal", n.natTraversal,
		"data", fmt.Sprintf("%x", data[:min(32, len(data))]))

	return nil
}

// ReceivePacket receives an IKE packet from UDP
func (n *NetworkHandler) ReceivePacket() ([]byte, error) {
	if n.conn == nil {
		return nil, fmt.Errorf("connection not established")
	}

	// Set read timeout
	if err := n.conn.SetReadDeadline(time.Now().Add(n.readTimeout)); err != nil {
		return nil, fmt.Errorf("failed to set read deadline: %w", err)
	}

	// Read packet
	buffer := make([]byte, 4096) // Maximum IKE packet size
	bytesRead, err := n.conn.Read(buffer)
	if err != nil {
		return nil, fmt.Errorf("failed to receive packet: %w", err)
	}

	packet := buffer[:bytesRead]

	// Remove Non-ESP marker if present
	if n.natTraversal && len(packet) >= 4 {
		// Check for Non-ESP marker
		if packet[0] == 0x00 && packet[1] == 0x00 && packet[2] == 0x00 && packet[3] == 0x00 {
			packet = packet[4:]
		}
	}

	n.logger.Debug("[SWu Client] packet received",
		"bytes", bytesRead,
		"nat_traversal", n.natTraversal,
		"data", fmt.Sprintf("%x", packet[:min(32, len(packet))]))

	return packet, nil
}

// SendReceive sends a packet and waits for response
func (n *NetworkHandler) SendReceive(data []byte) ([]byte, error) {
	// Send packet
	if err := n.SendPacket(data); err != nil {
		return nil, fmt.Errorf("failed to send packet: %w", err)
	}

	// Receive response
	response, err := n.ReceivePacket()
	if err != nil {
		return nil, fmt.Errorf("failed to receive response: %w", err)
	}

	return response, nil
}

// SetTimeouts sets read and write timeouts
func (n *NetworkHandler) SetTimeouts(read, write time.Duration) {
	n.readTimeout = read
	n.writeTimeout = write
	n.logger.Debug("[SWu Client] timeouts updated",
		"read_timeout", read,
		"write_timeout", write)
}

// GetLocalAddr returns the local UDP address
func (n *NetworkHandler) GetLocalAddr() *net.UDPAddr {
	return n.localAddr
}

// GetRemoteAddr returns the remote UDP address
func (n *NetworkHandler) GetRemoteAddr() *net.UDPAddr {
	return n.remoteAddr
}

// IsNATTraversal returns true if NAT traversal is enabled
func (n *NetworkHandler) IsNATTraversal() bool {
	return n.natTraversal
}

// Close closes the UDP connection
func (n *NetworkHandler) Close() error {
	if n.conn != nil {
		err := n.conn.Close()
		n.conn = nil
		n.logger.Info("[SWu Client] UDP connection closed")
		return err
	}
	return nil
}

// DetectNAT attempts to detect NAT by checking if local and remote addresses are on different networks
func (n *NetworkHandler) DetectNAT() bool {
	if n.localAddr == nil || n.remoteAddr == nil {
		return false
	}

	// Simple NAT detection: check if we're connecting to a public IP from a private IP
	localIP := n.localAddr.IP
	remoteIP := n.remoteAddr.IP

	// Check if local IP is private and remote IP is public
	isLocalPrivate := localIP.IsPrivate()
	isRemotePrivate := remoteIP.IsPrivate()

	natDetected := isLocalPrivate && !isRemotePrivate

	n.logger.Debug("[SWu Client] NAT detection",
		"local_ip", localIP.String(),
		"remote_ip", remoteIP.String(),
		"local_private", isLocalPrivate,
		"remote_private", isRemotePrivate,
		"nat_detected", natDetected)

	return natDetected
}

// SendKeepAlive sends a keep-alive packet to maintain NAT mapping
func (n *NetworkHandler) SendKeepAlive() error {
	if !n.natTraversal {
		return nil // No need for keep-alive without NAT traversal
	}

	// Send minimal packet with just Non-ESP marker
	keepAlive := make([]byte, 4)
	copy(keepAlive, n.nonESPMarker)

	if err := n.SendPacket(keepAlive); err != nil {
		return fmt.Errorf("failed to send keep-alive: %w", err)
	}

	n.logger.Debug("[SWu Client] NAT keep-alive sent")
	return nil
}

// StartKeepAlive starts periodic NAT keep-alive packets
func (n *NetworkHandler) StartKeepAlive(interval time.Duration) chan struct{} {
	stopChan := make(chan struct{})

	if !n.natTraversal {
		close(stopChan)
		return stopChan
	}

	go func() {
		ticker := time.NewTicker(interval)
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				if err := n.SendKeepAlive(); err != nil {
					n.logger.Error("[SWu Client] keep-alive failed", "error", err)
				}
			case <-stopChan:
				n.logger.Debug("[SWu Client] NAT keep-alive stopped")
				return
			}
		}
	}()

	n.logger.Info("[SWu Client] NAT keep-alive started", "interval", interval)
	return stopChan
}

// GetConnectionInfo returns connection information for debugging
func (n *NetworkHandler) GetConnectionInfo() map[string]interface{} {
	info := map[string]interface{}{
		"local_addr":    n.localAddr.String(),
		"remote_addr":   n.remoteAddr.String(),
		"nat_traversal": n.natTraversal,
		"nat_port":      n.natPort,
		"read_timeout":  n.readTimeout,
		"write_timeout": n.writeTimeout,
		"connected":     n.conn != nil,
	}

	return info
}
