package swuclient

import (
	"crypto/rand"
	"encoding/binary"
	"fmt"
	"log/slog"
	"time"
)

// IKEState represents the current state of IKE negotiation
type IKEState int

const (
	IKEStateInit IKEState = iota
	IKEStateSAInit
	IKEStateAuth
	IKEStateEstablished
	IKEStateFailed
)

// IKEStateMachine manages the IKE negotiation flow
type IKEStateMachine struct {
	logger *slog.Logger

	// State management
	currentState IKEState
	messageID    uint32

	// Components
	crypto  *Crypto
	network *NetworkHandler
	eapaka  *EAPAKAHandler

	// IKE parameters
	spiInitiator uint64
	spiResponder uint64

	// Nonces
	nonceInitiator []byte
	nonceResponder []byte

	// Configuration
	identity    string
	epdgAddress string
	usimAuth    func([]byte, []byte) ([]byte, []byte, []byte, error)

	// Timeouts and retries
	retryCount    int
	maxRetries    int
	retryInterval time.Duration
}

// NewIKEStateMachine creates a new IKE state machine
func NewIKEStateMachine(logger *slog.Logger, identity, epdgAddress string, usimAuth func([]byte, []byte) ([]byte, []byte, []byte, error)) (*IKEStateMachine, error) {
	// Initialize components
	crypto, err := NewCrypto(logger)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize crypto: %w", err)
	}

	network := NewNetworkHandler(logger)
	eapaka := NewEAPAKAHandler(logger, identity)

	// Generate initiator SPI
	spiBytes := make([]byte, 8)
	if _, err := rand.Read(spiBytes); err != nil {
		return nil, fmt.Errorf("failed to generate SPI: %w", err)
	}

	var spiInitiator uint64
	for i := 0; i < 8; i++ {
		spiInitiator = (spiInitiator << 8) | uint64(spiBytes[i])
	}

	sm := &IKEStateMachine{
		logger:        logger,
		currentState:  IKEStateInit,
		messageID:     0,
		crypto:        crypto,
		network:       network,
		eapaka:        eapaka,
		spiInitiator:  spiInitiator,
		identity:      identity,
		epdgAddress:   epdgAddress,
		usimAuth:      usimAuth,
		maxRetries:    3,
		retryInterval: 5 * time.Second,
	}

	logger.Info("[SWu Client] IKE state machine initialized",
		"identity", identity,
		"epdg", epdgAddress,
		"spi_initiator", fmt.Sprintf("%016x", spiInitiator))

	return sm, nil
}

// Start begins the IKE negotiation process
func (sm *IKEStateMachine) Start() error {
	sm.logger.Info("[SWu Client] starting IKE negotiation")

	// Connect to ePDG
	if err := sm.network.Connect(sm.epdgAddress, 0); err != nil {
		return fmt.Errorf("failed to connect to ePDG: %w", err)
	}
	defer sm.network.Close()

	// Detect NAT and enable traversal if needed
	if sm.network.DetectNAT() {
		sm.logger.Info("[SWu Client] NAT detected, enabling traversal")
		if err := sm.network.EnableNATTraversal(); err != nil {
			return fmt.Errorf("failed to enable NAT traversal: %w", err)
		}
	}

	// Execute IKE flow
	if err := sm.executeFlow(); err != nil {
		sm.currentState = IKEStateFailed
		return fmt.Errorf("IKE negotiation failed: %w", err)
	}

	sm.currentState = IKEStateEstablished
	sm.logger.Info("[SWu Client] IKE negotiation completed successfully")

	return nil
}

// executeFlow executes the complete IKE negotiation flow
func (sm *IKEStateMachine) executeFlow() error {
	// Phase 1: IKE_SA_INIT
	if err := sm.performSAInit(); err != nil {
		return fmt.Errorf("IKE_SA_INIT failed: %w", err)
	}

	// Phase 2: IKE_AUTH with EAP-AKA
	if err := sm.performAuth(); err != nil {
		return fmt.Errorf("IKE_AUTH failed: %w", err)
	}

	return nil
}

// performSAInit performs the IKE_SA_INIT exchange
func (sm *IKEStateMachine) performSAInit() error {
	sm.logger.Info("[SWu Client] starting IKE_SA_INIT exchange")
	sm.currentState = IKEStateSAInit

	// Generate initiator nonce
	sm.nonceInitiator = make([]byte, 32)
	if _, err := rand.Read(sm.nonceInitiator); err != nil {
		return fmt.Errorf("failed to generate nonce: %w", err)
	}

	// Create IKE_SA_INIT request
	request, err := sm.createSAInitRequest()
	if err != nil {
		return fmt.Errorf("failed to create SA_INIT request: %w", err)
	}

	// Send request and receive response
	response, err := sm.sendWithRetry(request)
	if err != nil {
		return fmt.Errorf("SA_INIT exchange failed: %w", err)
	}

	// Process SA_INIT response
	if err := sm.processSAInitResponse(response); err != nil {
		return fmt.Errorf("failed to process SA_INIT response: %w", err)
	}

	sm.logger.Info("[SWu Client] IKE_SA_INIT exchange completed")
	return nil
}

// performAuth performs the IKE_AUTH exchange with EAP-AKA
func (sm *IKEStateMachine) performAuth() error {
	sm.logger.Info("[SWu Client] starting IKE_AUTH exchange")
	sm.currentState = IKEStateAuth

	// Create initial IKE_AUTH request
	request, err := sm.createAuthRequest()
	if err != nil {
		return fmt.Errorf("failed to create AUTH request: %w", err)
	}

	// Send request and receive response
	response, err := sm.sendWithRetry(request)
	if err != nil {
		return fmt.Errorf("AUTH exchange failed: %w", err)
	}

	// Process AUTH response (should contain EAP-AKA Challenge)
	if err := sm.processAuthResponse(response); err != nil {
		return fmt.Errorf("failed to process AUTH response: %w", err)
	}

	sm.logger.Info("[SWu Client] IKE_AUTH exchange completed")
	return nil
}

// createSAInitRequest creates an IKE_SA_INIT request packet
func (sm *IKEStateMachine) createSAInitRequest() ([]byte, error) {
	// Create IKE header
	header := &IKEHeader{
		SPIInitiator: sm.spiInitiator,
		SPIResponder: 0, // Not set in initial request
		NextPayload:  PayloadTypeSA,
		Version:      (IKEMajorVersion << 4) | IKEMinorVersion,
		ExchangeType: ExchangeTypeIKESAInit,
		Flags:        FlagInitiator,
		MessageID:    sm.messageID,
	}

	// Create Security Association payload
	saPayload := &SecurityAssociationPayload{
		Header: PayloadHeader{
			NextPayload: PayloadTypeKE,
			Critical:    false,
		},
		// Add default proposal for IKEv2
		Proposals: []Proposal{
			{
				ProposalNumber: 1,
				ProtocolID:     ProtocolIDIKE,
				SPISize:        0,
				TransformCount: 4,
				Transforms: []Transform{
					{Type: TransformTypeEncr, TransformID: EncrAES_CBC, Attributes: []TransformAttribute{
						{Type: 0x800E, Value: []byte{0x00, 0x80}, Format: 1}, // Key Length = 128 bits
					}},
					{Type: TransformTypeInteg, TransformID: IntegHMACMD596},
					{Type: TransformTypePRF, TransformID: PRFHMACMD5},
					{Type: TransformTypeDH, TransformID: DHGroupMODP1024},
				},
			},
		},
	}

	// Create Key Exchange payload
	kePayload := &KeyExchangePayload{
		Header: PayloadHeader{
			NextPayload: PayloadTypeNonce,
			Critical:    false,
		},
		DHGroup: DHGroupMODP1024,
		Data:    sm.crypto.DHPublicKey.Bytes(),
	}

	// Create Nonce payload
	noncePayload := &NoncePayload{
		Header: PayloadHeader{
			NextPayload: PayloadTypeNone,
			Critical:    false,
		},
		Data: sm.nonceInitiator,
	}

	// Marshal all payloads
	saBytes, err := saPayload.MarshalBinary()
	if err != nil {
		return nil, fmt.Errorf("failed to marshal SA payload: %w", err)
	}

	keBytes, err := kePayload.MarshalBinary()
	if err != nil {
		return nil, fmt.Errorf("failed to marshal KE payload: %w", err)
	}

	nonceBytes, err := noncePayload.MarshalBinary()
	if err != nil {
		return nil, fmt.Errorf("failed to marshal Nonce payload: %w", err)
	}

	// Combine payloads
	payloads := append(saBytes, keBytes...)
	payloads = append(payloads, nonceBytes...)

	// Set header length
	header.Length = uint32(28 + len(payloads)) // IKE header is 28 bytes

	// Marshal header
	headerBytes, err := header.MarshalBinary()
	if err != nil {
		return nil, fmt.Errorf("failed to marshal IKE header: %w", err)
	}

	// Combine header and payloads
	packet := append(headerBytes, payloads...)

	sm.logger.Debug("[SWu Client] SA_INIT request created",
		"length", len(packet),
		"message_id", sm.messageID,
		"packet_hex", fmt.Sprintf("%x", packet[:min(64, len(packet))]))

	return packet, nil
}

// processSAInitResponse processes the IKE_SA_INIT response
func (sm *IKEStateMachine) processSAInitResponse(data []byte) error {
	sm.logger.Debug("[SWu Client] processing SA_INIT response",
		"data_length", len(data),
		"data_hex", fmt.Sprintf("%x", data))

	// Unmarshal IKE header
	header := &IKEHeader{}
	if err := header.UnmarshalBinary(data); err != nil {
		return fmt.Errorf("failed to unmarshal IKE header: %w", err)
	}

	sm.logger.Debug("[SWu Client] SA_INIT response header",
		"spi_initiator", fmt.Sprintf("%016x", header.SPIInitiator),
		"spi_responder", fmt.Sprintf("%016x", header.SPIResponder),
		"next_payload", header.NextPayload,
		"version", header.Version,
		"exchange_type", header.ExchangeType,
		"flags", header.Flags,
		"message_id", header.MessageID,
		"length", header.Length)

	// Store responder SPI
	sm.spiResponder = header.SPIResponder

	// Parse payloads to extract responder nonce, DH public key, and negotiated algorithms
	payloadData := data[28:] // IKE header is 28 bytes
	var responderPublicKey []byte
	var responderNonce []byte
	var negotiatedPRF, negotiatedInteg, negotiatedEncr, negotiatedEncrKeyLen uint16

	// Parse payloads - start with the first payload type from IKE header
	currentPayloadType := header.NextPayload
	offset := 0

	for offset < len(payloadData) && currentPayloadType != 0 {
		if offset+4 > len(payloadData) {
			break
		}

		nextPayloadType := payloadData[offset]     // Next payload type in chain
		payloadLength := binary.BigEndian.Uint16(payloadData[offset+2 : offset+4])

		if offset+int(payloadLength) > len(payloadData) {
			break
		}

		payloadContent := payloadData[offset : offset+int(payloadLength)]

		sm.logger.Debug("[SWu Client] processing payload", "type", currentPayloadType, "length", payloadLength)

		switch currentPayloadType {
		case PayloadTypeSA:
			sm.logger.Debug("[SWu Client] found SA payload, parsing algorithms")
			// Security Association payload: parse to extract negotiated algorithms
			if err := sm.parseNegotiatedAlgorithms(payloadContent, &negotiatedPRF, &negotiatedInteg, &negotiatedEncr, &negotiatedEncrKeyLen); err != nil {
				sm.logger.Warn("[SWu Client] failed to parse negotiated algorithms", "error", err)
				// Set default algorithms if parsing fails
				negotiatedPRF = PRFHMACMD5
				negotiatedInteg = IntegHMACMD596
				negotiatedEncr = EncrAES_CBC
				negotiatedEncrKeyLen = 128
			}
		case PayloadTypeKE:
			// Key Exchange payload: skip header (4 bytes) + DH Group (2 bytes) + Reserved (2 bytes)
			if len(payloadContent) > 8 {
				responderPublicKey = payloadContent[8:]
				sm.logger.Debug("[SWu Client] extracted responder DH public key",
					"length", len(responderPublicKey))
			}
		case PayloadTypeNonce:
			// Nonce payload: skip header (4 bytes)
			if len(payloadContent) > 4 {
				responderNonce = payloadContent[4:]
				sm.logger.Debug("[SWu Client] extracted responder nonce",
					"length", len(responderNonce))
			}
		}

		// Move to next payload
		currentPayloadType = nextPayloadType
		offset += int(payloadLength)
	}

	if len(responderPublicKey) == 0 {
		return fmt.Errorf("responder DH public key not found in SA_INIT response")
	}
	if len(responderNonce) == 0 {
		return fmt.Errorf("responder nonce not found in SA_INIT response")
	}

	sm.logger.Debug("[SWu Client] SA_INIT response processed",
		"spi_responder", fmt.Sprintf("%016x", sm.spiResponder),
		"message_id", header.MessageID,
		"responder_pubkey_len", len(responderPublicKey),
		"responder_nonce_len", len(responderNonce))

	// Generate shared DH key and derive IKE keys
	sm.crypto.SetSPIs(sm.spiInitiator, sm.spiResponder)

	// Set nonces
	sm.nonceResponder = responderNonce
	sm.crypto.SetNonces(sm.nonceInitiator, sm.nonceResponder)

	// Generate DH shared secret
	if err := sm.crypto.GenerateIKESecret(responderPublicKey); err != nil {
		return fmt.Errorf("failed to generate DH shared secret: %w", err)
	}

	// Set negotiated algorithms in crypto module
	sm.crypto.SetNegotiatedAlgorithms(negotiatedPRF, negotiatedInteg, negotiatedEncr, negotiatedEncrKeyLen)

	// Generate IKE keys
	if err := sm.crypto.GenerateIKEKeys(); err != nil {
		return fmt.Errorf("failed to generate IKE keys: %w", err)
	}

	// Increment message ID for next exchange
	sm.messageID++

	return nil
}

// parseNegotiatedAlgorithms parses the SA payload to extract negotiated algorithms
func (sm *IKEStateMachine) parseNegotiatedAlgorithms(saPayload []byte, prfAlg, integAlg, encrAlg, encrKeyLen *uint16) error {
	sm.logger.Debug("[SWu Client] parsing SA payload", "length", len(saPayload), "hex", fmt.Sprintf("%x", saPayload))

	// Skip SA payload header (4 bytes)
	if len(saPayload) < 12 {
		return fmt.Errorf("SA payload too short: %d bytes", len(saPayload))
	}

	offset := 4 // Skip payload header (Next Payload + Critical + Length)

	// Parse proposal header (8 bytes minimum)
	if offset+8 > len(saPayload) {
		return fmt.Errorf("proposal header too short")
	}

	// Proposal header: Last/More (1) + Reserved (1) + Proposal Length (2) +
	// Proposal Number (1) + Protocol ID (1) + SPI Size (1) + Transform Count (1)
	proposalLength := binary.BigEndian.Uint16(saPayload[offset+2 : offset+4])
	transformCount := saPayload[offset+7]
	spiSize := saPayload[offset+6]

	sm.logger.Debug("[SWu Client] proposal header",
		"proposal_length", proposalLength, "transform_count", transformCount, "spi_size", spiSize)

	offset += 8 + int(spiSize) // Skip proposal header + SPI

	// Parse transforms to extract algorithms
	for i := 0; i < int(transformCount); i++ {
		if offset+8 > len(saPayload) {
			sm.logger.Warn("[SWu Client] transform header too short", "offset", offset, "remaining", len(saPayload)-offset)
			break
		}

		// Transform header: Last/More (1) + Reserved (1) + Transform Length (2) +
		// Transform Type (1) + Reserved (1) + Transform ID (2)
		transformLength := binary.BigEndian.Uint16(saPayload[offset+2 : offset+4])
		transformType := saPayload[offset+4]
		transformID := binary.BigEndian.Uint16(saPayload[offset+6 : offset+8])

		sm.logger.Debug("[SWu Client] parsing transform",
			"index", i, "type", transformType, "id", transformID, "length", transformLength)

		switch transformType {
		case TransformTypeEncr:
			*encrAlg = transformID
			// Parse attributes for key length
			attrOffset := offset + 8
			for attrOffset < offset+int(transformLength) {
				if attrOffset+4 > len(saPayload) {
					break
				}
				attrType := binary.BigEndian.Uint16(saPayload[attrOffset : attrOffset+2])
				if attrType&0x8000 != 0 { // TV format
					if (attrType & 0x7FFF) == 0x000E { // Key Length attribute
						*encrKeyLen = binary.BigEndian.Uint16(saPayload[attrOffset+2 : attrOffset+4])
						sm.logger.Debug("[SWu Client] found key length attribute", "length", *encrKeyLen)
					}
					attrOffset += 4
				} else { // TLV format
					attrLen := binary.BigEndian.Uint16(saPayload[attrOffset+2 : attrOffset+4])
					attrOffset += 4 + int(attrLen)
				}
			}
		case TransformTypePRF:
			*prfAlg = transformID
		case TransformTypeInteg:
			*integAlg = transformID
		case TransformTypeDH:
			// DH Group - we don't need to store this as it's already negotiated
			sm.logger.Debug("[SWu Client] DH group", "group", transformID)
		}

		offset += int(transformLength)
	}

	sm.logger.Debug("[SWu Client] parsed negotiated algorithms",
		"prf", *prfAlg, "integ", *integAlg, "encr", *encrAlg, "encr_key_len", *encrKeyLen)

	return nil
}

// createAuthRequest creates an IKE_AUTH request packet
func (sm *IKEStateMachine) createAuthRequest() ([]byte, error) {
	// Create IKE header
	header := &IKEHeader{
		SPIInitiator: sm.spiInitiator,
		SPIResponder: sm.spiResponder,
		NextPayload:  PayloadTypeIDi,
		Version:      (IKEMajorVersion << 4) | IKEMinorVersion,
		ExchangeType: ExchangeTypeIKEAuth,
		Flags:        FlagInitiator,
		MessageID:    sm.messageID,
	}

	// Create Identification payload
	idPayload := &IdentificationPayload{
		Header: PayloadHeader{
			NextPayload: PayloadTypeAUTH,
			Critical:    false,
		},
		IDType: IDTypeRFC822Addr,
		Data:   []byte(sm.identity),
	}

	// Create Authentication payload (placeholder)
	authPayload := &AuthenticationPayload{
		Header: PayloadHeader{
			NextPayload: PayloadTypeCP,
			Critical:    false,
		},
		AuthMethod: AuthSharedKeyMessageIntegrityCode,
		Data:       make([]byte, 20), // Placeholder
	}

	// Create Configuration payload
	cpPayload := &ConfigurationPayload{
		Header: PayloadHeader{
			NextPayload: PayloadTypeNone,
			Critical:    false,
		},
		CFGType: CFGRequest,
		Attributes: []ConfigurationAttribute{
			{Type: AttrInternalIP4Address, Value: []byte{0, 0, 0, 0}},
			{Type: AttrInternalIP4DNS, Value: []byte{0, 0, 0, 0}},
		},
	}

	// Marshal payloads
	idBytes, err := idPayload.MarshalBinary()
	if err != nil {
		return nil, fmt.Errorf("failed to marshal ID payload: %w", err)
	}

	authBytes, err := authPayload.MarshalBinary()
	if err != nil {
		return nil, fmt.Errorf("failed to marshal Auth payload: %w", err)
	}

	cpBytes, err := cpPayload.MarshalBinary()
	if err != nil {
		return nil, fmt.Errorf("failed to marshal CP payload: %w", err)
	}

	// Combine payloads
	payloads := append(idBytes, authBytes...)
	payloads = append(payloads, cpBytes...)

	// Set header length
	header.Length = uint32(28 + len(payloads))

	// Marshal header
	headerBytes, err := header.MarshalBinary()
	if err != nil {
		return nil, fmt.Errorf("failed to marshal IKE header: %w", err)
	}

	// Combine header and payloads
	packet := append(headerBytes, payloads...)

	sm.logger.Debug("[SWu Client] AUTH request created",
		"length", len(packet),
		"message_id", sm.messageID)

	return packet, nil
}

// processAuthResponse processes the IKE_AUTH response
func (sm *IKEStateMachine) processAuthResponse(data []byte) error {
	// Unmarshal IKE header
	header := &IKEHeader{}
	if err := header.UnmarshalBinary(data); err != nil {
		return fmt.Errorf("failed to unmarshal IKE header: %w", err)
	}

	sm.logger.Debug("[SWu Client] AUTH response received",
		"message_id", header.MessageID,
		"length", len(data))

	// Parse payloads to find EAP payload
	// This is simplified - implement full payload parsing

	// Increment message ID for next exchange
	sm.messageID++

	return nil
}

// sendWithRetry sends a packet with retry logic
func (sm *IKEStateMachine) sendWithRetry(data []byte) ([]byte, error) {
	var lastErr error

	for attempt := 0; attempt <= sm.maxRetries; attempt++ {
		if attempt > 0 {
			sm.logger.Debug("[SWu Client] retrying packet send",
				"attempt", attempt,
				"max_retries", sm.maxRetries)
			time.Sleep(sm.retryInterval)
		}

		response, err := sm.network.SendReceive(data)
		if err != nil {
			lastErr = err
			continue
		}

		return response, nil
	}

	return nil, fmt.Errorf("failed after %d retries: %w", sm.maxRetries, lastErr)
}

// GetState returns the current IKE state
func (sm *IKEStateMachine) GetState() IKEState {
	return sm.currentState
}

// GetConnectionInfo returns connection information
func (sm *IKEStateMachine) GetConnectionInfo() map[string]interface{} {
	info := map[string]interface{}{
		"state":         sm.currentState,
		"message_id":    sm.messageID,
		"spi_initiator": fmt.Sprintf("%016x", sm.spiInitiator),
		"spi_responder": fmt.Sprintf("%016x", sm.spiResponder),
		"identity":      sm.identity,
		"epdg_address":  sm.epdgAddress,
		"retry_count":   sm.retryCount,
	}

	// Add network info
	if sm.network != nil {
		for k, v := range sm.network.GetConnectionInfo() {
			info["network_"+k] = v
		}
	}

	return info
}
