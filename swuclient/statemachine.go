package swuclient

import (
	"crypto/rand"
	"fmt"
	"log/slog"
	"time"
)

// IKEState represents the current state of IKE negotiation
type IKEState int

const (
	IKEStateInit IKEState = iota
	IKEStateSAInit
	IKEStateAuth
	IKEStateEstablished
	IKEStateFailed
)

// IKEStateMachine manages the IKE negotiation flow
type IKEStateMachine struct {
	logger *slog.Logger

	// State management
	currentState IKEState
	messageID    uint32

	// Components
	crypto  *Crypto
	network *NetworkHandler
	eapaka  *EAPAKAHandler

	// IKE parameters
	spiInitiator uint64
	spiResponder uint64

	// Nonces
	nonceInitiator []byte
	nonceResponder []byte

	// Configuration
	identity    string
	epdgAddress string
	usimAuth    func([]byte, []byte) ([]byte, []byte, []byte, error)

	// Timeouts and retries
	retryCount    int
	maxRetries    int
	retryInterval time.Duration
}

// NewIKEStateMachine creates a new IKE state machine
func NewIKEStateMachine(logger *slog.Logger, identity, epdgAddress string, usimAuth func([]byte, []byte) ([]byte, []byte, []byte, error)) (*IKEStateMachine, error) {
	// Initialize components
	crypto, err := NewCrypto(logger)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize crypto: %w", err)
	}

	network := NewNetworkHandler(logger)
	eapaka := NewEAPAKAHandler(logger, identity)

	// Generate initiator SPI
	spiBytes := make([]byte, 8)
	if _, err := rand.Read(spiBytes); err != nil {
		return nil, fmt.Errorf("failed to generate SPI: %w", err)
	}

	var spiInitiator uint64
	for i := 0; i < 8; i++ {
		spiInitiator = (spiInitiator << 8) | uint64(spiBytes[i])
	}

	sm := &IKEStateMachine{
		logger:        logger,
		currentState:  IKEStateInit,
		messageID:     0,
		crypto:        crypto,
		network:       network,
		eapaka:        eapaka,
		spiInitiator:  spiInitiator,
		identity:      identity,
		epdgAddress:   epdgAddress,
		usimAuth:      usimAuth,
		maxRetries:    3,
		retryInterval: 5 * time.Second,
	}

	logger.Info("[SWu Client] IKE state machine initialized",
		"identity", identity,
		"epdg", epdgAddress,
		"spi_initiator", fmt.Sprintf("%016x", spiInitiator))

	return sm, nil
}

// Start begins the IKE negotiation process
func (sm *IKEStateMachine) Start() error {
	sm.logger.Info("[SWu Client] starting IKE negotiation")

	// Connect to ePDG
	if err := sm.network.Connect(sm.epdgAddress, 0); err != nil {
		return fmt.Errorf("failed to connect to ePDG: %w", err)
	}
	defer sm.network.Close()

	// Detect NAT and enable traversal if needed
	if sm.network.DetectNAT() {
		sm.logger.Info("[SWu Client] NAT detected, enabling traversal")
		if err := sm.network.EnableNATTraversal(); err != nil {
			return fmt.Errorf("failed to enable NAT traversal: %w", err)
		}
	}

	// Execute IKE flow
	if err := sm.executeFlow(); err != nil {
		sm.currentState = IKEStateFailed
		return fmt.Errorf("IKE negotiation failed: %w", err)
	}

	sm.currentState = IKEStateEstablished
	sm.logger.Info("[SWu Client] IKE negotiation completed successfully")

	return nil
}

// executeFlow executes the complete IKE negotiation flow
func (sm *IKEStateMachine) executeFlow() error {
	// Phase 1: IKE_SA_INIT
	if err := sm.performSAInit(); err != nil {
		return fmt.Errorf("IKE_SA_INIT failed: %w", err)
	}

	// Phase 2: IKE_AUTH with EAP-AKA
	if err := sm.performAuth(); err != nil {
		return fmt.Errorf("IKE_AUTH failed: %w", err)
	}

	return nil
}

// performSAInit performs the IKE_SA_INIT exchange
func (sm *IKEStateMachine) performSAInit() error {
	sm.logger.Info("[SWu Client] starting IKE_SA_INIT exchange")
	sm.currentState = IKEStateSAInit

	// Generate initiator nonce
	sm.nonceInitiator = make([]byte, 32)
	if _, err := rand.Read(sm.nonceInitiator); err != nil {
		return fmt.Errorf("failed to generate nonce: %w", err)
	}

	// Create IKE_SA_INIT request
	request, err := sm.createSAInitRequest()
	if err != nil {
		return fmt.Errorf("failed to create SA_INIT request: %w", err)
	}

	// Send request and receive response
	response, err := sm.sendWithRetry(request)
	if err != nil {
		return fmt.Errorf("SA_INIT exchange failed: %w", err)
	}

	// Process SA_INIT response
	if err := sm.processSAInitResponse(response); err != nil {
		return fmt.Errorf("failed to process SA_INIT response: %w", err)
	}

	sm.logger.Info("[SWu Client] IKE_SA_INIT exchange completed")
	return nil
}

// performAuth performs the IKE_AUTH exchange with EAP-AKA
func (sm *IKEStateMachine) performAuth() error {
	sm.logger.Info("[SWu Client] starting IKE_AUTH exchange")
	sm.currentState = IKEStateAuth

	// Create initial IKE_AUTH request
	request, err := sm.createAuthRequest()
	if err != nil {
		return fmt.Errorf("failed to create AUTH request: %w", err)
	}

	// Send request and receive response
	response, err := sm.sendWithRetry(request)
	if err != nil {
		return fmt.Errorf("AUTH exchange failed: %w", err)
	}

	// Process AUTH response (should contain EAP-AKA Challenge)
	if err := sm.processAuthResponse(response); err != nil {
		return fmt.Errorf("failed to process AUTH response: %w", err)
	}

	sm.logger.Info("[SWu Client] IKE_AUTH exchange completed")
	return nil
}

// createSAInitRequest creates an IKE_SA_INIT request packet
func (sm *IKEStateMachine) createSAInitRequest() ([]byte, error) {
	// Create IKE header
	header := &IKEHeader{
		SPIInitiator: sm.spiInitiator,
		SPIResponder: 0, // Not set in initial request
		NextPayload:  PayloadTypeSA,
		Version:      (IKEMajorVersion << 4) | IKEMinorVersion,
		ExchangeType: ExchangeTypeIKESAInit,
		Flags:        FlagInitiator,
		MessageID:    sm.messageID,
	}

	// Create Security Association payload
	saPayload := &SecurityAssociationPayload{
		Header: PayloadHeader{
			NextPayload: PayloadTypeKE,
			Critical:    false,
		},
		// Add default proposal for IKEv2
		Proposals: []Proposal{
			{
				ProposalNumber: 1,
				ProtocolID:     ProtocolIDIKE,
				SPISize:        0,
				TransformCount: 4,
				Transforms: []Transform{
					{Type: TransformTypeEncr, TransformID: EncrAES_CBC},
					{Type: TransformTypeInteg, TransformID: IntegHMACSHA196},
					{Type: TransformTypePRF, TransformID: PRFHMACSHA256},
					{Type: TransformTypeDH, TransformID: DHGroupMODP1024},
				},
			},
		},
	}

	// Create Key Exchange payload
	kePayload := &KeyExchangePayload{
		Header: PayloadHeader{
			NextPayload: PayloadTypeNonce,
			Critical:    false,
		},
		DHGroup: DHGroupMODP1024,
		Data:    sm.crypto.DHPublicKey.Bytes(),
	}

	// Create Nonce payload
	noncePayload := &NoncePayload{
		Header: PayloadHeader{
			NextPayload: PayloadTypeNone,
			Critical:    false,
		},
		Data: sm.nonceInitiator,
	}

	// Marshal all payloads
	saBytes, err := saPayload.MarshalBinary()
	if err != nil {
		return nil, fmt.Errorf("failed to marshal SA payload: %w", err)
	}

	keBytes, err := kePayload.MarshalBinary()
	if err != nil {
		return nil, fmt.Errorf("failed to marshal KE payload: %w", err)
	}

	nonceBytes, err := noncePayload.MarshalBinary()
	if err != nil {
		return nil, fmt.Errorf("failed to marshal Nonce payload: %w", err)
	}

	// Combine payloads
	payloads := append(saBytes, keBytes...)
	payloads = append(payloads, nonceBytes...)

	// Set header length
	header.Length = uint32(28 + len(payloads)) // IKE header is 28 bytes

	// Marshal header
	headerBytes, err := header.MarshalBinary()
	if err != nil {
		return nil, fmt.Errorf("failed to marshal IKE header: %w", err)
	}

	// Combine header and payloads
	packet := append(headerBytes, payloads...)

	sm.logger.Debug("[SWu Client] SA_INIT request created",
		"length", len(packet),
		"message_id", sm.messageID)

	return packet, nil
}

// processSAInitResponse processes the IKE_SA_INIT response
func (sm *IKEStateMachine) processSAInitResponse(data []byte) error {
	// Unmarshal IKE header
	header := &IKEHeader{}
	if err := header.UnmarshalBinary(data); err != nil {
		return fmt.Errorf("failed to unmarshal IKE header: %w", err)
	}

	// Store responder SPI
	sm.spiResponder = header.SPIResponder

	// Parse payloads to extract responder nonce and DH public key
	// This is a simplified parser - in production, implement full payload parsing
	// For now, we'll extract the responder nonce and DH key from known positions
	// based on the reference implementations

	sm.logger.Debug("[SWu Client] SA_INIT response processed",
		"spi_responder", fmt.Sprintf("%016x", sm.spiResponder),
		"message_id", header.MessageID)

	// Generate shared DH key and derive IKE keys
	sm.crypto.SetSPIs(sm.spiInitiator, sm.spiResponder)

	// Set nonces (responder nonce would be extracted from response)
	sm.nonceResponder = make([]byte, 32) // Placeholder - extract from response
	sm.crypto.SetNonces(sm.nonceInitiator, sm.nonceResponder)

	// Generate IKE keys
	if err := sm.crypto.GenerateIKEKeys(); err != nil {
		return fmt.Errorf("failed to generate IKE keys: %w", err)
	}

	// Increment message ID for next exchange
	sm.messageID++

	return nil
}

// createAuthRequest creates an IKE_AUTH request packet
func (sm *IKEStateMachine) createAuthRequest() ([]byte, error) {
	// Create IKE header
	header := &IKEHeader{
		SPIInitiator: sm.spiInitiator,
		SPIResponder: sm.spiResponder,
		NextPayload:  PayloadTypeIDi,
		Version:      (IKEMajorVersion << 4) | IKEMinorVersion,
		ExchangeType: ExchangeTypeIKEAuth,
		Flags:        FlagInitiator,
		MessageID:    sm.messageID,
	}

	// Create Identification payload
	idPayload := &IdentificationPayload{
		Header: PayloadHeader{
			NextPayload: PayloadTypeAUTH,
			Critical:    false,
		},
		IDType: IDTypeRFC822Addr,
		Data:   []byte(sm.identity),
	}

	// Create Authentication payload (placeholder)
	authPayload := &AuthenticationPayload{
		Header: PayloadHeader{
			NextPayload: PayloadTypeCP,
			Critical:    false,
		},
		AuthMethod: AuthSharedKeyMessageIntegrityCode,
		Data:       make([]byte, 20), // Placeholder
	}

	// Create Configuration payload
	cpPayload := &ConfigurationPayload{
		Header: PayloadHeader{
			NextPayload: PayloadTypeNone,
			Critical:    false,
		},
		CFGType: CFGRequest,
		Attributes: []ConfigurationAttribute{
			{Type: AttrInternalIP4Address, Value: []byte{0, 0, 0, 0}},
			{Type: AttrInternalIP4DNS, Value: []byte{0, 0, 0, 0}},
		},
	}

	// Marshal payloads
	idBytes, err := idPayload.MarshalBinary()
	if err != nil {
		return nil, fmt.Errorf("failed to marshal ID payload: %w", err)
	}

	authBytes, err := authPayload.MarshalBinary()
	if err != nil {
		return nil, fmt.Errorf("failed to marshal Auth payload: %w", err)
	}

	cpBytes, err := cpPayload.MarshalBinary()
	if err != nil {
		return nil, fmt.Errorf("failed to marshal CP payload: %w", err)
	}

	// Combine payloads
	payloads := append(idBytes, authBytes...)
	payloads = append(payloads, cpBytes...)

	// Set header length
	header.Length = uint32(28 + len(payloads))

	// Marshal header
	headerBytes, err := header.MarshalBinary()
	if err != nil {
		return nil, fmt.Errorf("failed to marshal IKE header: %w", err)
	}

	// Combine header and payloads
	packet := append(headerBytes, payloads...)

	sm.logger.Debug("[SWu Client] AUTH request created",
		"length", len(packet),
		"message_id", sm.messageID)

	return packet, nil
}

// processAuthResponse processes the IKE_AUTH response
func (sm *IKEStateMachine) processAuthResponse(data []byte) error {
	// Unmarshal IKE header
	header := &IKEHeader{}
	if err := header.UnmarshalBinary(data); err != nil {
		return fmt.Errorf("failed to unmarshal IKE header: %w", err)
	}

	sm.logger.Debug("[SWu Client] AUTH response received",
		"message_id", header.MessageID,
		"length", len(data))

	// Parse payloads to find EAP payload
	// This is simplified - implement full payload parsing

	// Increment message ID for next exchange
	sm.messageID++

	return nil
}

// sendWithRetry sends a packet with retry logic
func (sm *IKEStateMachine) sendWithRetry(data []byte) ([]byte, error) {
	var lastErr error

	for attempt := 0; attempt <= sm.maxRetries; attempt++ {
		if attempt > 0 {
			sm.logger.Debug("[SWu Client] retrying packet send",
				"attempt", attempt,
				"max_retries", sm.maxRetries)
			time.Sleep(sm.retryInterval)
		}

		response, err := sm.network.SendReceive(data)
		if err != nil {
			lastErr = err
			continue
		}

		return response, nil
	}

	return nil, fmt.Errorf("failed after %d retries: %w", sm.maxRetries, lastErr)
}

// GetState returns the current IKE state
func (sm *IKEStateMachine) GetState() IKEState {
	return sm.currentState
}

// GetConnectionInfo returns connection information
func (sm *IKEStateMachine) GetConnectionInfo() map[string]interface{} {
	info := map[string]interface{}{
		"state":         sm.currentState,
		"message_id":    sm.messageID,
		"spi_initiator": fmt.Sprintf("%016x", sm.spiInitiator),
		"spi_responder": fmt.Sprintf("%016x", sm.spiResponder),
		"identity":      sm.identity,
		"epdg_address":  sm.epdgAddress,
		"retry_count":   sm.retryCount,
	}

	// Add network info
	if sm.network != nil {
		for k, v := range sm.network.GetConnectionInfo() {
			info["network_"+k] = v
		}
	}

	return info
}
