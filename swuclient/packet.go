package swuclient

import (
	"bytes"
	"encoding/binary"
	"fmt"
)

// <PERSON><PERSON>Head<PERSON> represents the IKEv2 header structure
type IKEHeader struct {
	SPIInitiator uint64 // 8 bytes
	SPIResponder uint64 // 8 bytes
	NextPayload  uint8  // 1 byte
	Version      uint8  // 1 byte (Major version 4 bits, Minor version 4 bits)
	ExchangeType uint8  // 1 byte
	Flags        uint8  // 1 byte
	MessageID    uint32 // 4 bytes
	Length       uint32 // 4 bytes
}

// NewIKEHeader creates a new IKE header with default values
func NewIKEHeader() *IKEHeader {
	return &IKEHeader{
		Version: (IKEMajorVersion << 4) | IKEMinorVersion,
	}
}

// MarshalBinary serializes the IKE header to binary format
func (h *IKEHeader) MarshalBinary() ([]byte, error) {
	buf := make([]byte, IKEHeaderSize)
	binary.BigEndian.PutUint64(buf[0:8], h.SPIInitiator)
	binary.BigEndian.PutUint64(buf[8:16], h.SPIResponder)
	buf[16] = h.NextPayload
	buf[17] = h.Version
	buf[18] = h.ExchangeType
	buf[19] = h.Flags
	binary.BigEndian.PutUint32(buf[20:24], h.MessageID)
	binary.BigEndian.PutUint32(buf[24:28], h.Length)
	return buf, nil
}

// UnmarshalBinary deserializes binary data into the IKE header
func (h *IKEHeader) UnmarshalBinary(data []byte) error {
	if len(data) < IKEHeaderSize {
		return fmt.Errorf("insufficient data for IKE header: got %d, need %d", len(data), IKEHeaderSize)
	}
	h.SPIInitiator = binary.BigEndian.Uint64(data[0:8])
	h.SPIResponder = binary.BigEndian.Uint64(data[8:16])
	h.NextPayload = data[16]
	h.Version = data[17]
	h.ExchangeType = data[18]
	h.Flags = data[19]
	h.MessageID = binary.BigEndian.Uint32(data[20:24])
	h.Length = binary.BigEndian.Uint32(data[24:28])
	return nil
}

// PayloadHeader represents the generic payload header
type PayloadHeader struct {
	NextPayload uint8  // 1 byte
	Critical    bool   // 1 bit (part of flags)
	Reserved    uint8  // 7 bits (part of flags)
	Length      uint16 // 2 bytes
}

// MarshalBinary serializes the payload header to binary format
func (p *PayloadHeader) MarshalBinary() ([]byte, error) {
	buf := make([]byte, 4)
	buf[0] = p.NextPayload
	flags := p.Reserved & 0x7F
	if p.Critical {
		flags |= 0x80
	}
	buf[1] = flags
	binary.BigEndian.PutUint16(buf[2:4], p.Length)
	return buf, nil
}

// UnmarshalBinary deserializes binary data into the payload header
func (p *PayloadHeader) UnmarshalBinary(data []byte) error {
	if len(data) < 4 {
		return fmt.Errorf("insufficient data for payload header: got %d, need 4", len(data))
	}
	p.NextPayload = data[0]
	flags := data[1]
	p.Critical = (flags & 0x80) != 0
	p.Reserved = flags & 0x7F
	p.Length = binary.BigEndian.Uint16(data[2:4])
	return nil
}

// Payload interface for all IKE payloads
type Payload interface {
	Type() uint8
	MarshalBinary() ([]byte, error)
	UnmarshalBinary(data []byte) error
}

// SecurityAssociationPayload represents the SA payload
type SecurityAssociationPayload struct {
	Header    PayloadHeader
	Proposals []Proposal
}

// Proposal represents a single proposal within an SA payload
type Proposal struct {
	ProposalNumber uint8
	ProtocolID     uint8
	SPISize        uint8
	TransformCount uint8
	SPI            []byte
	Transforms     []Transform
}

// Transform represents a single transform within a proposal
type Transform struct {
	Type        uint8
	TransformID uint16
	Attributes  []TransformAttribute
}

// TransformAttribute represents a transform attribute
type TransformAttribute struct {
	Type   uint16
	Value  []byte
	Format uint8 // 0 = TLV, 1 = TV
}

// Type returns the payload type
func (sa *SecurityAssociationPayload) Type() uint8 {
	return PayloadTypeSA
}

// MarshalBinary serializes the SA payload to binary format
func (sa *SecurityAssociationPayload) MarshalBinary() ([]byte, error) {
	var buf bytes.Buffer

	// Marshal proposals
	for i, proposal := range sa.Proposals {
		proposalData, err := proposal.MarshalBinary()
		if err != nil {
			return nil, fmt.Errorf("failed to marshal proposal %d: %w", i, err)
		}
		buf.Write(proposalData)
	}

	// Update header length
	sa.Header.Length = uint16(4 + buf.Len())

	// Marshal header
	headerData, err := sa.Header.MarshalBinary()
	if err != nil {
		return nil, fmt.Errorf("failed to marshal SA header: %w", err)
	}

	// Combine header and payload data
	result := make([]byte, 0, len(headerData)+buf.Len())
	result = append(result, headerData...)
	result = append(result, buf.Bytes()...)

	return result, nil
}

// UnmarshalBinary deserializes binary data into the SA payload
func (sa *SecurityAssociationPayload) UnmarshalBinary(data []byte) error {
	if len(data) < 4 {
		return fmt.Errorf("insufficient data for SA payload header")
	}

	// Unmarshal header
	if err := sa.Header.UnmarshalBinary(data[:4]); err != nil {
		return fmt.Errorf("failed to unmarshal SA header: %w", err)
	}

	// Parse proposals
	offset := 4
	for offset < len(data) {
		var proposal Proposal
		consumed, err := proposal.UnmarshalBinary(data[offset:])
		if err != nil {
			return fmt.Errorf("failed to unmarshal proposal: %w", err)
		}
		sa.Proposals = append(sa.Proposals, proposal)
		offset += consumed
	}

	return nil
}

// MarshalBinary serializes the proposal to binary format
func (p *Proposal) MarshalBinary() ([]byte, error) {
	var buf bytes.Buffer

	// Proposal header (8 bytes + SPI)
	buf.WriteByte(0) // Last proposal (will be updated by caller if needed)
	buf.WriteByte(0) // Reserved

	// Calculate proposal length
	proposalLen := 8 + len(p.SPI)
	for _, transform := range p.Transforms {
		transformData, err := transform.MarshalBinary()
		if err != nil {
			return nil, fmt.Errorf("failed to marshal transform: %w", err)
		}
		proposalLen += len(transformData)
	}

	binary.Write(&buf, binary.BigEndian, uint16(proposalLen))
	buf.WriteByte(p.ProposalNumber)
	buf.WriteByte(p.ProtocolID)
	buf.WriteByte(p.SPISize)
	buf.WriteByte(uint8(len(p.Transforms)))
	buf.Write(p.SPI)

	// Marshal transforms
	for _, transform := range p.Transforms {
		transformData, err := transform.MarshalBinary()
		if err != nil {
			return nil, fmt.Errorf("failed to marshal transform: %w", err)
		}
		buf.Write(transformData)
	}

	return buf.Bytes(), nil
}

// UnmarshalBinary deserializes binary data into the proposal and returns bytes consumed
func (p *Proposal) UnmarshalBinary(data []byte) (int, error) {
	if len(data) < 8 {
		return 0, fmt.Errorf("insufficient data for proposal header")
	}

	// Skip first 2 bytes (last proposal flag and reserved)
	proposalLen := binary.BigEndian.Uint16(data[2:4])
	if len(data) < int(proposalLen) {
		return 0, fmt.Errorf("insufficient data for proposal: got %d, need %d", len(data), proposalLen)
	}

	p.ProposalNumber = data[4]
	p.ProtocolID = data[5]
	p.SPISize = data[6]
	p.TransformCount = data[7]

	// Read SPI
	if len(data) < 8+int(p.SPISize) {
		return 0, fmt.Errorf("insufficient data for SPI")
	}
	p.SPI = make([]byte, p.SPISize)
	copy(p.SPI, data[8:8+p.SPISize])

	// Parse transforms
	offset := 8 + int(p.SPISize)
	for i := 0; i < int(p.TransformCount); i++ {
		var transform Transform
		consumed, err := transform.UnmarshalBinary(data[offset:])
		if err != nil {
			return 0, fmt.Errorf("failed to unmarshal transform %d: %w", i, err)
		}
		p.Transforms = append(p.Transforms, transform)
		offset += consumed
	}

	return int(proposalLen), nil
}

// MarshalBinary serializes the transform to binary format
func (t *Transform) MarshalBinary() ([]byte, error) {
	var buf bytes.Buffer

	// Calculate transform length
	transformLen := 8 // Base transform header
	for _, attr := range t.Attributes {
		if attr.Format == 1 { // TV format
			transformLen += 4
		} else { // TLV format
			transformLen += 4 + len(attr.Value)
		}
	}

	// Transform header
	buf.WriteByte(0) // Last transform (will be updated by caller if needed)
	buf.WriteByte(0) // Reserved
	binary.Write(&buf, binary.BigEndian, uint16(transformLen))
	buf.WriteByte(t.Type)
	buf.WriteByte(0) // Reserved
	binary.Write(&buf, binary.BigEndian, t.TransformID)

	// Marshal attributes
	for _, attr := range t.Attributes {
		attrData, err := attr.MarshalBinary()
		if err != nil {
			return nil, fmt.Errorf("failed to marshal attribute: %w", err)
		}
		buf.Write(attrData)
	}

	return buf.Bytes(), nil
}

// UnmarshalBinary deserializes binary data into the transform and returns bytes consumed
func (t *Transform) UnmarshalBinary(data []byte) (int, error) {
	if len(data) < 8 {
		return 0, fmt.Errorf("insufficient data for transform header")
	}

	// Skip first 2 bytes (last transform flag and reserved)
	transformLen := binary.BigEndian.Uint16(data[2:4])
	if len(data) < int(transformLen) {
		return 0, fmt.Errorf("insufficient data for transform: got %d, need %d", len(data), transformLen)
	}

	t.Type = data[4]
	// Skip reserved byte at data[5]
	t.TransformID = binary.BigEndian.Uint16(data[6:8])

	// Parse attributes
	offset := 8
	for offset < int(transformLen) {
		var attr TransformAttribute
		consumed, err := attr.UnmarshalBinary(data[offset:])
		if err != nil {
			return 0, fmt.Errorf("failed to unmarshal attribute: %w", err)
		}
		t.Attributes = append(t.Attributes, attr)
		offset += consumed
	}

	return int(transformLen), nil
}

// MarshalBinary serializes the transform attribute to binary format
func (ta *TransformAttribute) MarshalBinary() ([]byte, error) {
	if ta.Format == 1 { // TV format (Type/Value)
		buf := make([]byte, 4)
		binary.BigEndian.PutUint16(buf[0:2], ta.Type|0x8000) // Set AF bit for TV format
		if len(ta.Value) >= 2 {
			binary.BigEndian.PutUint16(buf[2:4], binary.BigEndian.Uint16(ta.Value[:2]))
		}
		return buf, nil
	} else { // TLV format (Type/Length/Value)
		buf := make([]byte, 4+len(ta.Value))
		binary.BigEndian.PutUint16(buf[0:2], ta.Type) // AF bit clear for TLV format
		binary.BigEndian.PutUint16(buf[2:4], uint16(len(ta.Value)))
		copy(buf[4:], ta.Value)
		return buf, nil
	}
}

// UnmarshalBinary deserializes binary data into the transform attribute and returns bytes consumed
func (ta *TransformAttribute) UnmarshalBinary(data []byte) (int, error) {
	if len(data) < 4 {
		return 0, fmt.Errorf("insufficient data for transform attribute")
	}

	typeField := binary.BigEndian.Uint16(data[0:2])
	ta.Type = typeField & 0x7FFF // Clear AF bit

	if (typeField & 0x8000) != 0 { // TV format
		ta.Format = 1
		ta.Value = make([]byte, 2)
		binary.BigEndian.PutUint16(ta.Value, binary.BigEndian.Uint16(data[2:4]))
		return 4, nil
	} else { // TLV format
		ta.Format = 0
		length := binary.BigEndian.Uint16(data[2:4])
		if len(data) < 4+int(length) {
			return 0, fmt.Errorf("insufficient data for TLV attribute value")
		}
		ta.Value = make([]byte, length)
		copy(ta.Value, data[4:4+length])
		return 4 + int(length), nil
	}
}

// KeyExchangePayload represents the KE payload
type KeyExchangePayload struct {
	Header  PayloadHeader
	DHGroup uint16
	Data    []byte
}

// Type returns the payload type
func (ke *KeyExchangePayload) Type() uint8 {
	return PayloadTypeKE
}

// MarshalBinary serializes the KE payload to binary format
func (ke *KeyExchangePayload) MarshalBinary() ([]byte, error) {
	// Update header length
	ke.Header.Length = uint16(4 + 4 + len(ke.Data)) // header + DH group + reserved + data

	// Marshal header
	headerData, err := ke.Header.MarshalBinary()
	if err != nil {
		return nil, fmt.Errorf("failed to marshal KE header: %w", err)
	}

	// Create payload data
	payloadData := make([]byte, 4+len(ke.Data))
	binary.BigEndian.PutUint16(payloadData[0:2], ke.DHGroup)
	// payloadData[2:4] are reserved (zero)
	copy(payloadData[4:], ke.Data)

	// Combine header and payload data
	result := make([]byte, 0, len(headerData)+len(payloadData))
	result = append(result, headerData...)
	result = append(result, payloadData...)

	return result, nil
}

// UnmarshalBinary deserializes binary data into the KE payload
func (ke *KeyExchangePayload) UnmarshalBinary(data []byte) error {
	if len(data) < 8 {
		return fmt.Errorf("insufficient data for KE payload")
	}

	// Unmarshal header
	if err := ke.Header.UnmarshalBinary(data[:4]); err != nil {
		return fmt.Errorf("failed to unmarshal KE header: %w", err)
	}

	// Parse payload data
	ke.DHGroup = binary.BigEndian.Uint16(data[4:6])
	// Skip reserved bytes at data[6:8]

	// Extract key exchange data
	dataLen := int(ke.Header.Length) - 8
	if len(data) < 8+dataLen {
		return fmt.Errorf("insufficient data for KE data")
	}
	ke.Data = make([]byte, dataLen)
	copy(ke.Data, data[8:8+dataLen])

	return nil
}

// NoncePayload represents the Nonce payload
type NoncePayload struct {
	Header PayloadHeader
	Data   []byte
}

// Type returns the payload type
func (n *NoncePayload) Type() uint8 {
	return PayloadTypeNonce
}

// MarshalBinary serializes the Nonce payload to binary format
func (n *NoncePayload) MarshalBinary() ([]byte, error) {
	// Update header length
	n.Header.Length = uint16(4 + len(n.Data))

	// Marshal header
	headerData, err := n.Header.MarshalBinary()
	if err != nil {
		return nil, fmt.Errorf("failed to marshal Nonce header: %w", err)
	}

	// Combine header and nonce data
	result := make([]byte, 0, len(headerData)+len(n.Data))
	result = append(result, headerData...)
	result = append(result, n.Data...)

	return result, nil
}

// UnmarshalBinary deserializes binary data into the Nonce payload
func (n *NoncePayload) UnmarshalBinary(data []byte) error {
	if len(data) < 4 {
		return fmt.Errorf("insufficient data for Nonce payload header")
	}

	// Unmarshal header
	if err := n.Header.UnmarshalBinary(data[:4]); err != nil {
		return fmt.Errorf("failed to unmarshal Nonce header: %w", err)
	}

	// Extract nonce data
	dataLen := int(n.Header.Length) - 4
	if len(data) < 4+dataLen {
		return fmt.Errorf("insufficient data for Nonce data")
	}
	n.Data = make([]byte, dataLen)
	copy(n.Data, data[4:4+dataLen])

	return nil
}

// IdentificationPayload represents the ID payload (both IDi and IDr)
type IdentificationPayload struct {
	Header PayloadHeader
	IDType uint8
	Data   []byte
}

// Type returns the payload type (must be set by caller to IDi or IDr)
func (id *IdentificationPayload) Type() uint8 {
	// This will be overridden by specific ID payload types
	return PayloadTypeIDi
}

// MarshalBinary serializes the ID payload to binary format
func (id *IdentificationPayload) MarshalBinary() ([]byte, error) {
	// Update header length
	id.Header.Length = uint16(4 + 4 + len(id.Data)) // header + ID type + reserved + data

	// Marshal header
	headerData, err := id.Header.MarshalBinary()
	if err != nil {
		return nil, fmt.Errorf("failed to marshal ID header: %w", err)
	}

	// Create payload data
	payloadData := make([]byte, 4+len(id.Data))
	payloadData[0] = id.IDType
	// payloadData[1:4] are reserved (zero)
	copy(payloadData[4:], id.Data)

	// Combine header and payload data
	result := make([]byte, 0, len(headerData)+len(payloadData))
	result = append(result, headerData...)
	result = append(result, payloadData...)

	return result, nil
}

// UnmarshalBinary deserializes binary data into the ID payload
func (id *IdentificationPayload) UnmarshalBinary(data []byte) error {
	if len(data) < 8 {
		return fmt.Errorf("insufficient data for ID payload")
	}

	// Unmarshal header
	if err := id.Header.UnmarshalBinary(data[:4]); err != nil {
		return fmt.Errorf("failed to unmarshal ID header: %w", err)
	}

	// Parse payload data
	id.IDType = data[4]
	// Skip reserved bytes at data[5:8]

	// Extract identification data
	dataLen := int(id.Header.Length) - 8
	if len(data) < 8+dataLen {
		return fmt.Errorf("insufficient data for ID data")
	}
	id.Data = make([]byte, dataLen)
	copy(id.Data, data[8:8+dataLen])

	return nil
}

// EAPPayload represents the EAP payload
type EAPPayload struct {
	Header  PayloadHeader
	Code    uint8
	ID      uint8
	Length  uint16
	EAPType uint8
	Data    []byte
}

// Type returns the payload type
func (eap *EAPPayload) Type() uint8 {
	return PayloadTypeEAP
}

// MarshalBinary serializes the EAP payload to binary format
func (eap *EAPPayload) MarshalBinary() ([]byte, error) {
	// Calculate EAP message length
	eapMsgLen := 5 + len(eap.Data) // Code + ID + Length + Type + Data
	eap.Length = uint16(eapMsgLen)

	// Update header length
	eap.Header.Length = uint16(4 + eapMsgLen)

	// Marshal header
	headerData, err := eap.Header.MarshalBinary()
	if err != nil {
		return nil, fmt.Errorf("failed to marshal EAP header: %w", err)
	}

	// Create EAP message
	eapMsg := make([]byte, eapMsgLen)
	eapMsg[0] = eap.Code
	eapMsg[1] = eap.ID
	binary.BigEndian.PutUint16(eapMsg[2:4], eap.Length)
	eapMsg[4] = eap.EAPType
	copy(eapMsg[5:], eap.Data)

	// Combine header and EAP message
	result := make([]byte, 0, len(headerData)+len(eapMsg))
	result = append(result, headerData...)
	result = append(result, eapMsg...)

	return result, nil
}

// UnmarshalBinary deserializes binary data into the EAP payload
func (eap *EAPPayload) UnmarshalBinary(data []byte) error {
	if len(data) < 9 {
		return fmt.Errorf("insufficient data for EAP payload")
	}

	// Unmarshal header
	if err := eap.Header.UnmarshalBinary(data[:4]); err != nil {
		return fmt.Errorf("failed to unmarshal EAP header: %w", err)
	}

	// Parse EAP message
	eap.Code = data[4]
	eap.ID = data[5]
	eap.Length = binary.BigEndian.Uint16(data[6:8])
	eap.EAPType = data[8]

	// Extract EAP data
	dataLen := int(eap.Length) - 5
	if len(data) < 9+dataLen {
		return fmt.Errorf("insufficient data for EAP data")
	}
	eap.Data = make([]byte, dataLen)
	copy(eap.Data, data[9:9+dataLen])

	return nil
}

// ConfigurationPayload represents the CP payload
type ConfigurationPayload struct {
	Header     PayloadHeader
	CFGType    uint8
	Attributes []ConfigurationAttribute
}

// ConfigurationAttribute represents a configuration attribute
type ConfigurationAttribute struct {
	Type   uint16
	Length uint16
	Value  []byte
}

// Type returns the payload type
func (cp *ConfigurationPayload) Type() uint8 {
	return PayloadTypeCP
}

// MarshalBinary serializes the CP payload to binary format
func (cp *ConfigurationPayload) MarshalBinary() ([]byte, error) {
	var buf bytes.Buffer

	// CFG Type and reserved bytes
	buf.WriteByte(cp.CFGType)
	buf.WriteByte(0) // Reserved
	buf.WriteByte(0) // Reserved
	buf.WriteByte(0) // Reserved

	// Marshal attributes
	for _, attr := range cp.Attributes {
		attrData, err := attr.MarshalBinary()
		if err != nil {
			return nil, fmt.Errorf("failed to marshal configuration attribute: %w", err)
		}
		buf.Write(attrData)
	}

	// Update header length
	cp.Header.Length = uint16(4 + buf.Len())

	// Marshal header
	headerData, err := cp.Header.MarshalBinary()
	if err != nil {
		return nil, fmt.Errorf("failed to marshal CP header: %w", err)
	}

	// Combine header and payload data
	result := make([]byte, 0, len(headerData)+buf.Len())
	result = append(result, headerData...)
	result = append(result, buf.Bytes()...)

	return result, nil
}

// UnmarshalBinary deserializes binary data into the CP payload
func (cp *ConfigurationPayload) UnmarshalBinary(data []byte) error {
	if len(data) < 8 {
		return fmt.Errorf("insufficient data for CP payload")
	}

	// Unmarshal header
	if err := cp.Header.UnmarshalBinary(data[:4]); err != nil {
		return fmt.Errorf("failed to unmarshal CP header: %w", err)
	}

	// Parse CFG type
	cp.CFGType = data[4]
	// Skip reserved bytes at data[5:8]

	// Parse attributes
	offset := 8
	for offset < len(data) {
		var attr ConfigurationAttribute
		consumed, err := attr.UnmarshalBinary(data[offset:])
		if err != nil {
			return fmt.Errorf("failed to unmarshal configuration attribute: %w", err)
		}
		cp.Attributes = append(cp.Attributes, attr)
		offset += consumed
	}

	return nil
}

// MarshalBinary serializes the configuration attribute to binary format
func (ca *ConfigurationAttribute) MarshalBinary() ([]byte, error) {
	buf := make([]byte, 4+len(ca.Value))
	binary.BigEndian.PutUint16(buf[0:2], ca.Type)
	binary.BigEndian.PutUint16(buf[2:4], uint16(len(ca.Value)))
	copy(buf[4:], ca.Value)
	return buf, nil
}

// UnmarshalBinary deserializes binary data into the configuration attribute and returns bytes consumed
func (ca *ConfigurationAttribute) UnmarshalBinary(data []byte) (int, error) {
	if len(data) < 4 {
		return 0, fmt.Errorf("insufficient data for configuration attribute")
	}

	ca.Type = binary.BigEndian.Uint16(data[0:2])
	ca.Length = binary.BigEndian.Uint16(data[2:4])

	if len(data) < 4+int(ca.Length) {
		return 0, fmt.Errorf("insufficient data for configuration attribute value")
	}

	ca.Value = make([]byte, ca.Length)
	copy(ca.Value, data[4:4+ca.Length])

	return 4 + int(ca.Length), nil
}

// NotifyPayload represents the Notify payload
type NotifyPayload struct {
	Header      PayloadHeader
	ProtocolID  uint8
	SPISize     uint8
	MessageType uint16
	SPI         []byte
	Data        []byte
}

// Type returns the payload type
func (n *NotifyPayload) Type() uint8 {
	return PayloadTypeNotify
}

// MarshalBinary serializes the Notify payload to binary format
func (n *NotifyPayload) MarshalBinary() ([]byte, error) {
	// Update header length
	n.Header.Length = uint16(4 + 4 + len(n.SPI) + len(n.Data))

	// Marshal header
	headerData, err := n.Header.MarshalBinary()
	if err != nil {
		return nil, fmt.Errorf("failed to marshal Notify header: %w", err)
	}

	// Create payload data
	payloadData := make([]byte, 4+len(n.SPI)+len(n.Data))
	payloadData[0] = n.ProtocolID
	payloadData[1] = n.SPISize
	binary.BigEndian.PutUint16(payloadData[2:4], n.MessageType)
	copy(payloadData[4:], n.SPI)
	copy(payloadData[4+len(n.SPI):], n.Data)

	// Combine header and payload data
	result := make([]byte, 0, len(headerData)+len(payloadData))
	result = append(result, headerData...)
	result = append(result, payloadData...)

	return result, nil
}

// UnmarshalBinary deserializes binary data into the Notify payload
func (n *NotifyPayload) UnmarshalBinary(data []byte) error {
	if len(data) < 8 {
		return fmt.Errorf("insufficient data for Notify payload")
	}

	// Unmarshal header
	if err := n.Header.UnmarshalBinary(data[:4]); err != nil {
		return fmt.Errorf("failed to unmarshal Notify header: %w", err)
	}

	// Parse payload data
	n.ProtocolID = data[4]
	n.SPISize = data[5]
	n.MessageType = binary.BigEndian.Uint16(data[6:8])

	// Extract SPI
	if len(data) < 8+int(n.SPISize) {
		return fmt.Errorf("insufficient data for Notify SPI")
	}
	n.SPI = make([]byte, n.SPISize)
	copy(n.SPI, data[8:8+n.SPISize])

	// Extract notification data
	dataLen := int(n.Header.Length) - 8 - int(n.SPISize)
	if len(data) < 8+int(n.SPISize)+dataLen {
		return fmt.Errorf("insufficient data for Notify data")
	}
	n.Data = make([]byte, dataLen)
	copy(n.Data, data[8+int(n.SPISize):8+int(n.SPISize)+dataLen])

	return nil
}

// AuthenticationPayload represents the AUTH payload
type AuthenticationPayload struct {
	Header     PayloadHeader
	AuthMethod uint8
	Data       []byte
}

// Type returns the payload type
func (auth *AuthenticationPayload) Type() uint8 {
	return PayloadTypeAUTH
}

// MarshalBinary serializes the AUTH payload to binary format
func (auth *AuthenticationPayload) MarshalBinary() ([]byte, error) {
	// Update header length
	auth.Header.Length = uint16(4 + 4 + len(auth.Data))

	// Marshal header
	headerData, err := auth.Header.MarshalBinary()
	if err != nil {
		return nil, fmt.Errorf("failed to marshal AUTH header: %w", err)
	}

	// Create payload data
	payloadData := make([]byte, 4+len(auth.Data))
	payloadData[0] = auth.AuthMethod
	// payloadData[1:4] are reserved (zero)
	copy(payloadData[4:], auth.Data)

	// Combine header and payload data
	result := make([]byte, 0, len(headerData)+len(payloadData))
	result = append(result, headerData...)
	result = append(result, payloadData...)

	return result, nil
}

// UnmarshalBinary deserializes binary data into the AUTH payload
func (auth *AuthenticationPayload) UnmarshalBinary(data []byte) error {
	if len(data) < 8 {
		return fmt.Errorf("insufficient data for AUTH payload")
	}

	// Unmarshal header
	if err := auth.Header.UnmarshalBinary(data[:4]); err != nil {
		return fmt.Errorf("failed to unmarshal AUTH header: %w", err)
	}

	// Parse payload data
	auth.AuthMethod = data[4]
	// Skip reserved bytes at data[5:8]

	// Extract authentication data
	dataLen := int(auth.Header.Length) - 8
	if len(data) < 8+dataLen {
		return fmt.Errorf("insufficient data for AUTH data")
	}
	auth.Data = make([]byte, dataLen)
	copy(auth.Data, data[8:8+dataLen])

	return nil
}
