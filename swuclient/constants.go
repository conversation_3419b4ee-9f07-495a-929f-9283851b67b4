package swuclient

// IKEv2 Protocol Constants based on RFC 7296 and reference implementations

// IKEv2 Payload Types
const (
	PayloadTypeNone     = 0
	PayloadTypeSA       = 33
	PayloadTypeKE       = 34
	PayloadTypeIDi      = 35
	PayloadTypeIDr      = 36
	PayloadTypeCERT     = 37
	PayloadTypeCERTREQ  = 38
	PayloadTypeAUTH     = 39
	PayloadTypeNonce    = 40
	PayloadTypeNotify   = 41
	PayloadTypeDelete   = 42
	PayloadTypeVendorID = 43
	PayloadTypeTSi      = 44
	PayloadTypeTSr      = 45
	PayloadTypeSK       = 46
	PayloadTypeCP       = 47
	PayloadTypeEAP      = 48
)

// IKEv2 Exchange Types
const (
	ExchangeTypeIKESAInit     = 34
	ExchangeTypeIKEAuth       = 35
	ExchangeTypeCreateChildSA = 36
	ExchangeTypeInformational = 37
)

// IKEv2 Protocol IDs
const (
	ProtocolIDReserved = 0
	ProtocolIDIKE      = 1
	ProtocolIDAH       = 2
	ProtocolIDESP      = 3
)

// Transform Types
const (
	TransformTypeEncr  = 1
	TransformTypePRF   = 2
	TransformTypeInteg = 3
	TransformTypeDH    = 4
	TransformTypeESN   = 5
)

// Transform Type 1 - Encryption Algorithm Transform IDs
const (
	EncrDES_IV64           = 1
	EncrDES                = 2
	Encr3DES               = 3
	EncrRC5                = 4
	EncrIDEA               = 5
	EncrCAST               = 6
	EncrBLOWFISH           = 7
	Encr3IDEA              = 8
	EncrDES_IV32           = 9
	EncrNULL               = 11
	EncrAES_CBC            = 12
	EncrAES_CTR            = 13
	EncrAES_CCM_8          = 14
	EncrAES_CCM_12         = 15
	EncrAES_CCM_16         = 16
	EncrAES_GCM_8          = 18
	EncrAES_GCM_12         = 19
	EncrAES_GCM_16         = 20
	EncrNULL_AUTH_AES_GMAC = 21
	EncrCHACHA20_POLY1305  = 28
)

// Transform Type 2 - Pseudorandom Function Transform IDs
const (
	PRFReserved    = 0
	PRFHMACMD5     = 1
	PRFHMACSHA1    = 2
	PRFHMACSHA256  = 5
	PRFHMACSHA384  = 6
	PRFHMACSHA512  = 7
	PRFAES128_XCBC = 4
	PRFAES128_CMAC = 8
)

// Transform Type 3 - Integrity Algorithm Transform IDs
const (
	IntegNone           = 0
	IntegHMACMD596      = 1
	IntegHMACSHA196     = 2
	IntegDESMAC         = 3
	IntegKPDKMD5        = 4
	IntegAESXCBC96      = 5
	IntegHMACMD5128     = 6
	IntegHMACSHA1160    = 7
	IntegAESCMAC96      = 8
	IntegAES128GMAC     = 9
	IntegAES192GMAC     = 10
	IntegAES256GMAC     = 11
	IntegHMACSHA2256128 = 12
	IntegHMACSHA2384192 = 13
	IntegHMACSHA2512256 = 14
)

// Transform Type 4 - Diffie-Hellman Group Transform IDs
const (
	DHGroupMODP768  = 1
	DHGroupMODP1024 = 2
	DHGroupMODP1536 = 5
	DHGroupMODP2048 = 14
	DHGroupMODP3072 = 15
	DHGroupMODP4096 = 16
	DHGroupMODP6144 = 17
	DHGroupMODP8192 = 18
)

// IKEv2 Notify Message Types - Error Types
const (
	NotifyUnsupportedCriticalPayload = 1
	NotifyInvalidIKESPI              = 4
	NotifyInvalidMajorVersion        = 5
	NotifyInvalidSyntax              = 7
	NotifyInvalidMessageID           = 9
	NotifyInvalidSPI                 = 11
	NotifyNoProposalChosen           = 14
	NotifyInvalidKEPayload           = 17
	NotifyAuthenticationFailed       = 24
	NotifySinglePairRequired         = 34
	NotifyNoAdditionalSAS            = 35
	NotifyInternalAddressFailure     = 36
	NotifyFailedCPRequired           = 37
	NotifyTSUnacceptable             = 38
	NotifyInvalidSelectors           = 39
	NotifyUnacceptableAddresses      = 40
	NotifyUnexpectedNATDetected      = 41
	NotifyUseAssignedHOA             = 42
	NotifyTemporaryFailure           = 43
	NotifyChildSANotFound            = 44
	NotifyInvalidGroupID             = 45
	NotifyAuthorizationFailed        = 46
)

// IKEv2 Notify Message Types - Status Types
const (
	NotifyInitialContact              = 16384
	NotifySetWindowSize               = 16385
	NotifyAdditionalTSPossible        = 16386
	NotifyIPCOMPSupported             = 16387
	NotifyNATDetectionSourceIP        = 16388
	NotifyNATDetectionDestinationIP   = 16389
	NotifyCookie                      = 16390
	NotifyUseTransportMode            = 16391
	NotifyHTTPCertLookupSupported     = 16392
	NotifyRekeySA                     = 16393
	NotifyESPTFCPaddingNotSupported   = 16394
	NotifyNonFirstFragmentsAlso       = 16395
	NotifyMobIKESupported             = 16396
	NotifyAdditionalIP4Address        = 16397
	NotifyAdditionalIP6Address        = 16398
	NotifyNoAdditionalAddresses       = 16399
	NotifyUpdateSAAddresses           = 16400
	NotifyCookieRequired              = 16401
	NotifyNATDetectionSourceIPv6      = 16402
	NotifyNATDetectionDestinationIPv6 = 16403
	NotifyEAPOnlyAuthentication       = 40960
	NotifyDeviceIdentity              = 40961
)

// IKEv2 Authentication Method
const (
	AuthRSADigitalSignature           = 1
	AuthSharedKeyMessageIntegrityCode = 2
	AuthDSSDigitalSignature           = 3
)

// IKEv2 Traffic Selector Types
const (
	TSTypeIPv4AddrRange = 7
	TSTypeIPv6AddrRange = 8
)

// IP Protocol IDs
const (
	IPProtocolAny  = 0
	IPProtocolICMP = 1
	IPProtocolTCP  = 6
	IPProtocolUDP  = 17
	IPProtocolESP  = 50
)

// IKEv2 Configuration Payload CFG Types
const (
	CFGRequest = 1
	CFGReply   = 2
	CFGSet     = 3
	CFGAck     = 4
)

// Configuration Attribute Types
const (
	AttrInternalIP4Address            = 1
	AttrInternalIP4Netmask            = 2
	AttrInternalIP4DNS                = 3
	AttrInternalIP4NBNS               = 4
	AttrInternalAddressExpiry         = 5
	AttrInternalIP4DHCP               = 6
	AttrApplicationVersion            = 7
	AttrInternalIP6Address            = 8
	AttrInternalIP6DNS                = 10
	AttrInternalIP6DHCP               = 11
	AttrInternalIP4Subnet             = 13
	AttrSupportedAttributes           = 14
	AttrInternalIP6Subnet             = 15
	AttrMIP6HomePrefix                = 16
	AttrInternalIP6Link               = 17
	AttrInternalIP6Prefix             = 18
	AttrHomeAgentAddress              = 19
	AttrPCSCFIP4Address               = 20
	AttrPCSCFIP6Address               = 21
	AttrFTTKAT                        = 22
	AttrExternalSourceIP4NATInfo      = 23
	AttrTimeoutPeriodForLivenessCheck = 24
	AttrInternalDNSDomain             = 25
	AttrInternalDNSSECTA              = 26
)

// IKEv2 Identification Payload ID Types
const (
	IDTypeIPv4Addr   = 1
	IDTypeFQDN       = 2
	IDTypeRFC822Addr = 3
	IDTypeIPv6Addr   = 5
	IDTypeDERASN1DN  = 9
	IDTypeDERASN1GN  = 10
	IDTypeKeyID      = 11
	IDTypeFCName     = 12
	IDTypeNull       = 13
)

// EAP Code Types
const (
	EAPCodeRequest  = 1
	EAPCodeResponse = 2
	EAPCodeSuccess  = 3
	EAPCodeFailure  = 4
)

// EAP Types
const (
	EAPTypeAKA = 23
)

// EAP-AKA Subtypes
const (
	EAPAKASubtypeChallenge = 1
	EAPAKASubtypeResponse  = 2
)

// EAP-AKA Attribute Types
const (
	ATRand            = 1
	ATAutn            = 2
	ATRes             = 3
	ATAuts            = 4
	ATPadding         = 6
	ATNonceMT         = 7
	ATMac             = 11
	ATCounter         = 19
	ATCounterTooSmall = 20
	ATNonceS          = 21
	ATClientErrorCode = 22
	ATKDF             = 24
	ATBidding         = 136
	ATIV              = 129
	ATEncrData        = 130
	ATNextPseudonym   = 132
	ATNextReauth      = 133
	ATCheckcode       = 134
	ATResult          = 135
)

// Default Ports
const (
	IKEPort             = 500
	IKENATTraversalPort = 4500
)

// IKE Version
const (
	IKEMajorVersion = 2
	IKEMinorVersion = 0
)

// IKE Header Flags
const (
	FlagInitiator = 0x08
	FlagVersion   = 0x10
	FlagResponse  = 0x20
)

// Packet sizes and limits
const (
	IKEHeaderSize    = 28
	MaxPacketSize    = 1500
	NonESPMarkerSize = 4
)
