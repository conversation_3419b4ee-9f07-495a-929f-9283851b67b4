package swuclient

import (
	"context"
	"fmt"
	"log/slog"
	"time"

	"github.com/damonto/vowifi/usim"
)

// SWuClient represents the main SWu IKEv2 client
type SWuClient struct {
	ctx    *context.Context
	logger *slog.Logger
	option *Option

	// Core components
	stateMachine *IKEStateMachine
	crypto       *Crypto
	network      *NetworkHandler
	eapaka       *EAPAKAHandler

	// Connection state
	connected    bool
	tunnelActive bool
}

// Option contains configuration options for the SWu client
type Option struct {
	USIM    *usim.USIM
	IMEI    *usim.IMEI
	Address string
	NAI     string

	// Connection options
	LocalPort     int
	Timeout       time.Duration
	MaxRetries    int
	RetryInterval time.Duration
}

func (o *Option) setDefaults() error {
	imsi, err := o.USIM.GetIMSI()
	if err != nil {
		return err
	}
	mnc := imsi.MNC()
	mcc := imsi.MCC()
	if o.Address == "" {
		o.Address = fmt.Sprintf("epdg.epc.mnc%s.mcc%s.pub.3gppnetwork.org", mnc, mcc)
	}
	o.NAI = fmt.Sprintf("<EMAIL>%s.mcc%s.3gppnetwork.org", imsi.String(), mnc, mcc)

	// Set default connection options
	if o.LocalPort == 0 {
		o.LocalPort = 0 // Use random port
	}
	if o.Timeout == 0 {
		o.Timeout = 30 * time.Second
	}
	if o.MaxRetries == 0 {
		o.MaxRetries = 3
	}
	if o.RetryInterval == 0 {
		o.RetryInterval = 5 * time.Second
	}

	return nil
}

func NewClient(ctx *context.Context, opts *Option, logger *slog.Logger) (*SWuClient, error) {
	if opts == nil {
		return nil, fmt.Errorf("options cannot be nil")
	}
	if err := opts.setDefaults(); err != nil {
		return nil, fmt.Errorf("failed to set defaults: %w", err)
	}

	// Create USIM authentication function
	usimAuth := func(rand, autn []byte) ([]byte, []byte, []byte, error) {
		return opts.USIM.Auth(rand, autn)
	}

	// Initialize state machine
	stateMachine, err := NewIKEStateMachine(logger, opts.NAI, opts.Address, usimAuth)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize state machine: %w", err)
	}

	client := &SWuClient{
		ctx:          ctx,
		logger:       logger,
		option:       opts,
		stateMachine: stateMachine,
		connected:    false,
		tunnelActive: false,
	}

	logger.Info("[SWu Client] client initialized",
		"identity", opts.NAI,
		"epdg_address", opts.Address)

	return client, nil
}

// Connect establishes the SWu IKEv2 connection to ePDG
func (c *SWuClient) Connect() error {
	c.logger.Info("[SWu Client] starting connection to ePDG")

	// Start the IKE negotiation
	if err := c.stateMachine.Start(); err != nil {
		c.logger.Error("[SWu Client] connection failed", "error", err)
		return fmt.Errorf("failed to establish SWu connection: %w", err)
	}

	c.connected = true
	c.logger.Info("[SWu Client] SWu IKEv2 connection established successfully")

	return nil
}

// Disconnect closes the SWu connection
func (c *SWuClient) Disconnect() error {
	c.logger.Info("[SWu Client] disconnecting from ePDG")

	// TODO: Send proper IKE delete notifications

	c.connected = false
	c.tunnelActive = false

	c.logger.Info("[SWu Client] disconnected from ePDG")
	return nil
}

// IsConnected returns true if the SWu connection is established
func (c *SWuClient) IsConnected() bool {
	return c.connected
}

// IsTunnelActive returns true if the IPSec tunnel is active
func (c *SWuClient) IsTunnelActive() bool {
	return c.tunnelActive
}

// GetConnectionInfo returns detailed connection information
func (c *SWuClient) GetConnectionInfo() map[string]interface{} {
	info := map[string]interface{}{
		"connected":      c.connected,
		"tunnel_active":  c.tunnelActive,
		"identity":       c.option.NAI,
		"epdg_address":   c.option.Address,
		"local_port":     c.option.LocalPort,
		"timeout":        c.option.Timeout,
		"max_retries":    c.option.MaxRetries,
		"retry_interval": c.option.RetryInterval,
	}

	// Add state machine info if available
	if c.stateMachine != nil {
		for k, v := range c.stateMachine.GetConnectionInfo() {
			info["ike_"+k] = v
		}
	}

	return info
}

// GetUSIMInfo returns USIM information
func (c *SWuClient) GetUSIMInfo() (map[string]string, error) {
	imsi, err := c.option.USIM.GetIMSI()
	if err != nil {
		return nil, fmt.Errorf("failed to get IMSI: %w", err)
	}

	info := map[string]string{
		"imsi": imsi.String(),
		"mcc":  imsi.MCC(),
		"mnc":  imsi.MNC(),
		"nai":  c.option.NAI,
	}

	if c.option.IMEI != nil {
		info["imei"] = c.option.IMEI.String()
	}

	return info, nil
}

// TestUSIMAuth tests USIM authentication with real values from ePDG
func (c *SWuClient) TestUSIMAuth() error {
	c.logger.Info("[SWu Client] USIM authentication will be tested during EAP-AKA challenge")
	c.logger.Info("[SWu Client] Skipping dummy authentication test - will use real RAND/AUTN from ePDG")
	return nil
}

// GetState returns the current IKE state
func (c *SWuClient) GetState() IKEState {
	if c.stateMachine == nil {
		return IKEStateInit
	}
	return c.stateMachine.GetState()
}

// SetLogger updates the logger for the client and all components
func (c *SWuClient) SetLogger(logger *slog.Logger) {
	c.logger = logger
	if c.stateMachine != nil {
		c.stateMachine.logger = logger
	}
}
