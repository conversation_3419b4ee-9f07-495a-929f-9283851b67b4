package swuclient

import (
	"context"
	"fmt"
	"log/slog"

	"github.com/damonto/vowifi/usim"
)

type SWuClient struct {
	ctx    *context.Context
	logger *slog.Logger
	option *Option
}

type Option struct {
	usim    *usim.USIM
	IMEI    *usim.IMEI
	Address string
	NAI     string
}

func (o *Option) setDefaults() error {
	imsi, err := o.usim.GetIMSI()
	if err != nil {
		return err
	}
	mnc := imsi.MNC()
	mcc := imsi.MCC()
	if o.Address == "" {
		o.Address = fmt.Sprintf("epdg.epc.mnc%s.mcc%s.pub.3gppnetwork.org", mnc, mcc)
	}
	o.NAI = fmt.Sprintf("<EMAIL>%s.mcc%s.3gppnetwork.org", imsi.String(), mnc, mcc)
	return nil
}

func NewClient(ctx *context.Context, opts *Option, logger *slog.Logger) (*SWuClient, error) {
	if opts == nil {
		return nil, fmt.Errorf("options cannot be nil")
	}
	if err := opts.setDefaults(); err != nil {
		return nil, fmt.Errorf("failed to set defaults: %w", err)
	}
	client := &SWuClient{
		ctx:    ctx,
		logger: logger,
		option: opts,
	}
	// Initialize client with options if needed

	return client, nil
}
