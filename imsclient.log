[2025-06-29 7:14:29](IMSClient): [SUC<PERSON>] IMSClient master
[2025-06-29 7:14:29](PCSCUICC<PERSON><PERSON>ider): [INFO] Found reader: Alcor Link AK9563 00 00
[2025-06-29 7:14:29](PCSC<PERSON><PERSON><PERSON><PERSON><PERSON>): [SUCC] Use reader: Alcor Link AK9563 00 00
[2025-06-29 7:14:29](Identity): [INFO] IMEI: 35665642-130527-6, IMSI: 310240184649727, MCC: 310, MNC: 240
[2025-06-29 7:14:29](Utils): [DEBUG] ip link del ims8ffdc031
Cannot find device "ims8ffdc031"
[2025-06-29 7:14:29](Utils): [DEBUG] ip xfrm state deleteall reqid 2415771697
[2025-06-29 7:14:29](SWuClient): [INFO] SWu connection initializing...
[2025-06-29 7:14:29](Utils): [DEBUG] iptables -t nat -I OUTPUT -m mark --mark 2415771697 -d ************** -j DNAT --to-destination ***********
[2025-06-29 7:14:30](SWuClient): [INFO] Generated ePDG Host: epdg.epc.mnc240.mcc310.pub.3gppnetwork.org, Addr: ***********, Mapped to: **************
[2025-06-29 7:14:30](SWuClient): [INFO] Initial IKE MSG=00, username: <EMAIL>
[2025-06-29 7:14:30](SWuClient): [INFO] Receive IKE MSG=00 Reply
[2025-06-29 7:14:30](CryptoHelper): [DEBUG] DH_KEY: aad599d111baaa689389530a552a87287afb4b062ae1350e2bbb3837d8f053393d99735e1b3911dd62ed007ac6d2563f71286d85031458308fa320caadad1aa442e6b57646c91aebd3b98f705d570f727b96993595bd3808a65bce0937bc9ce98a410cacb3aa3a2297e2ea0a5b10faeb998b867672c130654b10d99fab05516e
[2025-06-29 7:14:30](CryptoHelper): [DEBUG] NONCE: 16eb7bc667d25c085e8c9cadefc7d12a37ece1cbd69a61c0d6c0d4059ad86bac10e904a73e6bece18607cfe8028bcb7d
[2025-06-29 7:14:30](CryptoHelper): [DEBUG] SKEYSEED: 12394d828f572418202cfeea0a8bd2a1
[2025-06-29 7:14:30](CryptoHelper): [DEBUG] STREAM: 16eb7bc667d25c085e8c9cadefc7d12a37ece1cbd69a61c0d6c0d4059ad86bac10e904a73e6bece18607cfe8028bcb7d00000000bf1cb3ab112400668ff96c08
[2025-06-29 7:14:30](CryptoHelper): [DEBUG] sk_ d: d095435805a93d19e2784c43f1b8f5d9
[2025-06-29 7:14:30](CryptoHelper): [DEBUG] sk_ai: 38908f7167a9909e2a4817fa583e06d1
[2025-06-29 7:14:30](CryptoHelper): [DEBUG] sk_ar: dfd5478af3b335f669c3ae62cde9993e
[2025-06-29 7:14:30](CryptoHelper): [DEBUG] sk_ei: c7df75dea2fad771b3bd8b16b2e2e0fc
[2025-06-29 7:14:30](CryptoHelper): [DEBUG] sk_er: e15a67b78594a476ca0779b95010e3af
[2025-06-29 7:14:30](CryptoHelper): [DEBUG] sk_pi: 3a8060c89672194440197f01bd238964
[2025-06-29 7:14:30](CryptoHelper): [DEBUG] sk_pr: 62107829bd3c3e52306760c302d7a13b
[2025-06-29 7:14:30](SWuClient): [DEBUG] Decrypt Table: 00000000bf1cb3ab,112400668ff96c08,c7df75dea2fad771b3bd8b16b2e2e0fc,e15a67b78594a476ca0779b95010e3af,"AES-CBC-128 [RFC3602]",38908f7167a9909e2a4817fa583e06d1,dfd5478af3b335f669c3ae62cde9993e,"HMAC_MD5_96 [RFC2403]"
[2025-06-29 7:14:30](SWuClient): [INFO] Initial secure IKE MID=01 EAP-AKA Request
[2025-06-29 7:14:30](IKEAuthInitiatorRequestPacket): [DEBUG] [PHP_DEBUG] Adding payloads in order:
[2025-06-29 7:14:30](IKEAuthInitiatorRequestPacket): [DEBUG] [PHP_DEBUG] 1. Initiator ID payload - NAI: <EMAIL>
[2025-06-29 7:14:30](IKEAuthInitiatorRequestPacket): [DEBUG] [PHP_DEBUG] 2. Responder ID payload - NAI: ims
[2025-06-29 7:14:30](IKEAuthInitiatorRequestPacket): [DEBUG] [PHP_DEBUG] 3. Configuration Request payload
[2025-06-29 7:14:30](IKEAuthInitiatorRequestPacket): [DEBUG] [PHP_DEBUG] 4. ESP Security Association payload with 6 proposals
[2025-06-29 7:14:30](IKEAuthInitiatorRequestPacket): [DEBUG] [PHP_DEBUG] 5. Initiator Traffic Selector payload
[2025-06-29 7:14:30](IKEAuthInitiatorRequestPacket): [DEBUG] [PHP_DEBUG] 6. Responder Traffic Selector payload
[2025-06-29 7:14:30](IKEAuthInitiatorRequestPacket): [DEBUG] [PHP_DEBUG] 7. EAP-Only Authentication Notify payload
[2025-06-29 7:14:30](IKEAuthInitiatorRequestPacket): [DEBUG] [PHP_DEBUG] 8. Device Identity Notify payload - IMEI: 3566564213052700
[2025-06-29 7:14:30](IKEAuthInitiatorRequestPacket): [DEBUG] [PHP_DEBUG] Total payloads added: 8
[2025-06-29 7:14:30](EncryptedPacket): [DEBUG] [PHP_DEBUG] Encrypting packet with 8 payloads
[2025-06-29 7:14:30](EncryptedPacket): [DEBUG] [PHP_DEBUG] Payload 0: imsclient\protocol\isakmp\payload\InitiatorIdentificationPayload
[2025-06-29 7:14:30](EncryptedPacket): [DEBUG] [PHP_DEBUG] Payload 1: imsclient\protocol\isakmp\payload\ResponderIdentificationPayload
[2025-06-29 7:14:30](EncryptedPacket): [DEBUG] [PHP_DEBUG] Payload 2: imsclient\protocol\isakmp\payload\ConfigurationPayload
[2025-06-29 7:14:30](EncryptedPacket): [DEBUG] [PHP_DEBUG] Payload 3: imsclient\protocol\isakmp\payload\SecurityAssociationPayload
[2025-06-29 7:14:30](EncryptedPacket): [DEBUG] [PHP_DEBUG] Payload 4: imsclient\protocol\isakmp\payload\InitiatorTrafficSelectorPayload
[2025-06-29 7:14:30](EncryptedPacket): [DEBUG] [PHP_DEBUG] Payload 5: imsclient\protocol\isakmp\payload\ResponderTrafficSelectorPayload
[2025-06-29 7:14:30](EncryptedPacket): [DEBUG] [PHP_DEBUG] Payload 6: imsclient\protocol\isakmp\payload\NotifyPayload
[2025-06-29 7:14:30](EncryptedPacket): [DEBUG] [PHP_DEBUG] Payload 7: imsclient\protocol\isakmp\payload\DeviceIdentityNotifyPayload
[2025-06-29 7:14:30](ConfigurationPayload): [DEBUG] [PHP_DEBUG] Configuration payload type: 1
[2025-06-29 7:14:30](ConfigurationPayload): [DEBUG] [PHP_DEBUG] Configuration attributes count: 6
[2025-06-29 7:14:30](ConfigurationPayload): [DEBUG] [PHP_DEBUG] Configuration attribute 0: 00010000
[2025-06-29 7:14:30](ConfigurationPayload): [DEBUG] [PHP_DEBUG] Configuration attribute 1: 00030000
[2025-06-29 7:14:30](ConfigurationPayload): [DEBUG] [PHP_DEBUG] Configuration attribute 2: 00080000
[2025-06-29 7:14:30](ConfigurationPayload): [DEBUG] [PHP_DEBUG] Configuration attribute 3: 000a0000
[2025-06-29 7:14:30](ConfigurationPayload): [DEBUG] [PHP_DEBUG] Configuration attribute 4: 00140000
[2025-06-29 7:14:30](ConfigurationPayload): [DEBUG] [PHP_DEBUG] Configuration attribute 5: 00150000
[2025-06-29 7:14:30](ConfigurationPayload): [DEBUG] [PHP_DEBUG] Configuration payload total: 01000000000100000003000000080000000a00000014000000150000
[2025-06-29 7:14:30](DeviceIdentityNotifyPayload): [DEBUG] [PHP_DEBUG] Device Identity IMEI encoding:
[2025-06-29 7:14:30](DeviceIdentityNotifyPayload): [DEBUG] [PHP_DEBUG] Original IMEI: 3566564213052700
[2025-06-29 7:14:30](DeviceIdentityNotifyPayload): [DEBUG] [PHP_DEBUG] Number bytes length: 8
[2025-06-29 7:14:30](DeviceIdentityNotifyPayload): [DEBUG] [PHP_DEBUG] Padded IMEI: 3566564213052700
[2025-06-29 7:14:30](DeviceIdentityNotifyPayload): [DEBUG] [PHP_DEBUG] Digit-reversed IMEI: 5366652431507200
[2025-06-29 7:14:30](DeviceIdentityNotifyPayload): [DEBUG] [PHP_DEBUG] Binary IMEI: 5366652431507200
[2025-06-29 7:14:30](DeviceIdentityNotifyPayload): [DEBUG] [PHP_DEBUG] Final Device Identity data: 0009025366652431507200
[2025-06-29 7:14:30](EncryptedPacket): [DEBUG] [PHP_DEBUG] Unencrypted payload buffer length: 504
[2025-06-29 7:14:30](EncryptedPacket): [DEBUG] [PHP_DEBUG] Unencrypted payload buffer (first 64 bytes): 2400003e0300000030333130323430313834363439373237406e61692e6570632e6d6e633234302e6d63633331302e336770706e6574776f726b2e6f72672f00
[2025-06-29 7:14:30](EncryptedPacket): [DEBUG] [PHP_DEBUG] Padding length: 7, padded buffer length: 512
[2025-06-29 7:14:30](EncryptedPacket): [DEBUG] [PHP_DEBUG] Final packet buffer length before IC: 572
[2025-06-29 7:14:30](EncryptedPacket): [DEBUG] [PHP_DEBUG] Final encrypted packet length: 572
[2025-06-29 7:14:30](EncryptedPacket): [DEBUG] [PHP_DEBUG] Final encrypted packet hex: 00000000bf1cb3ab112400668ff96c082e202308000000010000023c2300022078fb61613ed8b66e44fc9f58a346923e8242e802d65ffbe4ee578b418dbbc7a2c2d2a4be46ba09ef7b539cc7272e70401e061c1f710bd46d91cb5d3f873f395471e2735d3bd926ab5a23c6efe4f810fa487a475fbb111cb2d42a3ae11673da273136d9ca2ac77e0a84d479040af92d104e24d229b4b32ba8dbf73c2b4afc61d603d554ff12094f8eff4ce66e2b92a24928b4914a5b4f0e363d8988de3bae1a12bbfc985e540a57bd5d909c32ce31ad7c3360c554119f6e4c49800b9acf7dcfe34ff900c3a00f2e136ac93a9610cf050dd5450caa62d88c6db4513cc69eb618b44ac1fd931a3aed03c06f57ae24ba61cdf14eb018222fc195fe08872c1c3281956b063761cd70c88ed31c3134420b53c85bef885a60765deec2dc88ebcfe0ec2d88f3e5d28784be78d7cfdadd914c9fe48184650e90f5a113eca20ac5907cd9992becb91c76fd09b2ec1dcc56b959a06ea24866af761398be1b7807adebcc74c49389f4a01edfa22f6537f07b0e291d93d528b5c7883186ebc0fbaf5fb84e921041b19633f403ecc04644d79e6012a27559e46d624e4ebf8f7112bd76dc908718dc7ae7b180ab7695d8c18b34c52d9da2b13268b7a4ed9fd5b0da97f9d47c173c9ebf7b7a7c1557e1cc03a7f98b441947312bcb2d6ecefc0b1dbdc8b2f19b49368b444dda3eaca63da81e97e0445d20b1ee812b370cd11f751f68d7ab37bc0b92f82fc8a6d084ac556a61327270d2f3c88eb50baff0a6bf7a553353bf
[2025-06-29 7:14:30](SWuClient): [INFO] Receive secure IKE MID=01
[DEBUG_EXTRACT] PHP EAP message raw data: 010100481701000001050000dd95eafea666968cf7e5b25d1667fff502050000c57d5900adc90000148e939f6acaa6480b050000a6fbb12903fedb4523548dd763e67b7286010000
[DEBUG_EXTRACT] PHP AT_RAND raw_value: 0000dd95eafea666968cf7e5b25d1667fff5
[DEBUG_EXTRACT] PHP AT_RAND extracted: dd95eafea666968cf7e5b25d1667fff5
[DEBUG_EXTRACT] PHP AT_AUTN raw_value: 0000c57d5900adc90000148e939f6acaa648
[DEBUG_EXTRACT] PHP AT_AUTN extracted: c57d5900adc90000148e939f6acaa648
[DEBUG] PHP USIM Auth - RAND: dd95eafea666968cf7e5b25d1667fff5
[DEBUG] PHP USIM Auth - AUTN: c57d5900adc90000148e939f6acaa648
[2025-06-29 7:14:30](SWuClient): [INFO] Initial secure IKE MID=02 EAP-AKA Response
[2025-06-29 7:14:30](EncryptedPacket): [DEBUG] [PHP_DEBUG] Encrypting packet with 1 payloads
[2025-06-29 7:14:30](EncryptedPacket): [DEBUG] [PHP_DEBUG] Payload 0: imsclient\protocol\isakmp\payload\ExtensibleAuthenticationPayload
[2025-06-29 7:14:30](EncryptedPacket): [DEBUG] [PHP_DEBUG] Unencrypted payload buffer length: 44
[2025-06-29 7:14:30](EncryptedPacket): [DEBUG] [PHP_DEBUG] Unencrypted payload buffer (first 64 bytes): 0000002c0201002817010000030300405345b26787e61b290b050000579b4c205a1ed9639f51e80baf7d2413
[2025-06-29 7:14:30](EncryptedPacket): [DEBUG] [PHP_DEBUG] Padding length: 3, padded buffer length: 48
[2025-06-29 7:14:30](EncryptedPacket): [DEBUG] [PHP_DEBUG] Final packet buffer length before IC: 108
[2025-06-29 7:14:30](EncryptedPacket): [DEBUG] [PHP_DEBUG] Final encrypted packet length: 108
[2025-06-29 7:14:30](EncryptedPacket): [DEBUG] [PHP_DEBUG] Final encrypted packet hex: 00000000bf1cb3ab112400668ff96c082e202308000000020000006c3000005038816d44dc3a9cd7e8ccc4ac82de37ba172a8a0e190ff3ab570d2400100da6c260ea099bcf46a6fd40c963a5dc1d81cf4701254bc5c57d0fd51aa9e1a8b392badd76ee1a48a53128a515f3de
[2025-06-29 7:14:31](SWuClient): [INFO] Receive secure IKE MSG=02 EAP-AKA Result
[2025-06-29 7:14:31](SWuClient): [SUCC] EAP-AKA Success
[2025-06-29 7:14:31](SWuClient): [INFO] Initial secure IKE MID=03 IKE_AUTH Request
[2025-06-29 7:14:31](IKEAuthRequest): [DEBUG] [PHP_DEBUG] ID payload raw data: 0300000030333130323430313834363439373237406e61692e6570632e6d6e633234302e6d63633331302e336770706e6574776f726b2e6f7267 (length: 58)
[2025-06-29 7:14:31](IKEAuthRequest): [DEBUG] [PHP_DEBUG] SK_PI key: 3a8060c89672194440197f01bd238964
[2025-06-29 7:14:31](IKEAuthRequest): [DEBUG] [PHP_DEBUG] ID payload hash (step 1): 67622d1577044aedc3a5f4781dd3affa
[2025-06-29 7:14:31](IKEAuthRequest): [DEBUG] [PHP_DEBUG] IKE_SA_INIT buffer length: 508
[2025-06-29 7:14:31](IKEAuthRequest): [DEBUG] [PHP_DEBUG] Nonce responder: 37ece1cbd69a61c0d6c0d4059ad86bac10e904a73e6bece18607cfe8028bcb7d (length: 32)
[2025-06-29 7:14:31](IKEAuthRequest): [DEBUG] [PHP_DEBUG] Combined packet data length: 556
[2025-06-29 7:14:31](IKEAuthRequest): [DEBUG] [PHP_DEBUG] Combined packet data (first 64 bytes): 00000000bf1cb3ab00000000000000002120220800000000000001fc2200010c0200002c010100040300000c0100000c800e0080030000080300000103000008
[2025-06-29 7:14:31](IKEAuthRequest): [DEBUG] [PHP_DEBUG] EAP-MSK: d01ff4caed92c9661966844c6b9094155dab82cbe290fca12a02100de1536eeeedfe2e18c6aca782a0c46434dc3d57320f06a2cf8955eee55f9cc5ef8f357baa (length: 64)
[2025-06-29 7:14:31](IKEAuthRequest): [DEBUG] [PHP_DEBUG] Temporary key: 5baab73dfcf2e3321ef08a57d9b820b9
[2025-06-29 7:14:31](IKEAuthRequest): [DEBUG] [PHP_DEBUG] Final authentication hash: 1d315005d086469f226cf7eda2a3a068
[2025-06-29 7:14:31](EncryptedPacket): [DEBUG] [PHP_DEBUG] Encrypting packet with 1 payloads
[2025-06-29 7:14:31](EncryptedPacket): [DEBUG] [PHP_DEBUG] Payload 0: imsclient\protocol\isakmp\payload\AuthenticationPayload
[2025-06-29 7:14:31](EncryptedPacket): [DEBUG] [PHP_DEBUG] Unencrypted payload buffer length: 24
[2025-06-29 7:14:31](EncryptedPacket): [DEBUG] [PHP_DEBUG] Unencrypted payload buffer (first 64 bytes): 00000018020000001d315005d086469f226cf7eda2a3a068
[2025-06-29 7:14:31](EncryptedPacket): [DEBUG] [PHP_DEBUG] Padding length: 7, padded buffer length: 32
[2025-06-29 7:14:31](EncryptedPacket): [DEBUG] [PHP_DEBUG] Final packet buffer length before IC: 92
[2025-06-29 7:14:31](EncryptedPacket): [DEBUG] [PHP_DEBUG] Final encrypted packet length: 92
[2025-06-29 7:14:31](EncryptedPacket): [DEBUG] [PHP_DEBUG] Final encrypted packet hex: 00000000bf1cb3ab112400668ff96c082e202308000000030000005c270000406e896bae0cd5d9e18f44c4c5b07512b0bfcb40613547eb647716835309dd33933ef658d90bc3dc1940e802023348b5c9ff3c4332f795770bef2d0272
[2025-06-29 7:14:31](SWuClient): [INFO] Receive secure IKE MSG=03 IKE_AUTH Result
[2025-06-29 7:14:31](Utils): [DEBUG] ip xfrm state add src ************ dst ************** proto esp spi 27836414 mode tunnel enc "cbc(aes)" "0xeaf4b938a6ec8260da52c44efb56889a" auth-trunc "hmac(md5)" "0xcecccc316098f22f77d25bb4a85b0498" 96 encap espinudp 37088 4500 0.0.0.0 sel src ::/0 dst ::/0 output-mark 2415771697 reqid 2415771697
[2025-06-29 7:14:31](Utils): [DEBUG] ip xfrm state add src ************** dst ************ proto esp spi 3563981929 mode tunnel enc "cbc(aes)" "0xb97224d1dd5b1012cb6c5449b73a1733" auth-trunc "hmac(md5)" "0xb12efb36694c0df73ff1134bca0647a6" 96 encap espinudp 4500 37088 0.0.0.0 sel src ::/0 dst ::/0 reqid 2415771697
[2025-06-29 7:14:31](Utils): [DEBUG] ip xfrm policy add src ::/0 dst ::/0 dir out tmpl src ************ dst ************** proto esp spi 27836414 mode tunnel reqid 2415771697 mark 2415771697
[2025-06-29 7:14:31](Utils): [DEBUG] ip xfrm policy add src ::/0 dst ::/0 dir in tmpl src ************** dst ************ proto esp spi 3563981929 mode tunnel reqid 2415771697 mark 2415771697
[2025-06-29 7:14:31](Utils): [DEBUG] ip link add ims8ffdc031 type vti local ************ remote ************** key 2415771697
[2025-06-29 7:14:31](Utils): [DEBUG] ip addr add 2607:fc20:571f:9bab:ac39:bec2:64e7:407c/128 dev ims8ffdc031
[2025-06-29 7:14:31](Utils): [DEBUG] ip link set ims8ffdc031 mtu 1280 up
[2025-06-29 7:14:31](Utils): [DEBUG] ip route add fd00:976a:14fe:30::5 src 2607:fc20:571f:9bab:ac39:bec2:64e7:407c dev ims8ffdc031
[2025-06-29 7:14:31](SWuClient): [SUCC] SWu connection established
[2025-06-29 7:14:31](IMSClient): [INFO] P-CSCF Addr: fd00:976a:14fe:30::5
[2025-06-29 7:14:31](IMSSocketPool): [INFO] Binding sockets on: [2607:fc20:571f:9bab:ac39:bec2:64e7:407c]
[2025-06-29 7:14:31](IMSSocketPool): [DEBUG] Creating sockets...
[2025-06-29 7:14:31](IMSSocketPool): [INFO] Self Port Initial: 18043, Client: 11719, Server: 47654
[2025-06-29 7:14:31](IMSSocketPool): [INFO] Starting sockets...
[2025-06-29 7:14:32](IMSSocketPool): [SUCC] Initial connected to: [fd00:976a:14fe:30::5]:5060
[2025-06-29 7:14:32](RegisterSequence): [INFO] Initial secure negotiation, username: sip:<EMAIL>
[2025-06-29 7:14:32](RegisterSequence): [INFO] Received server challenge & IPSec configration
[DEBUG] PHP USIM Auth - RAND: 7ae7fd62b7c5a583b13bb89ba92c4af8
[DEBUG] PHP USIM Auth - AUTN: f4916a48678a00001f55b8f735fc9002
[2025-06-29 7:14:32](Identity): [DEBUG] Digest Challenge H(Response): 97c91f365acf1de26e16fb153664c5a9
[2025-06-29 7:14:32](Utils): [DEBUG] ip xfrm state add src 2607:fc20:571f:9bab:ac39:bec2:64e7:407c dst fd00:976a:14fe:30::5 proto esp spi 2552538371 mode transport enc "cipher_null" "" auth-trunc "hmac(md5)" "0x0b5b41a3d6efb3a65ae176833145ee86" 96 reqid 2415771697
[2025-06-29 7:14:32](Utils): [DEBUG] ip xfrm state add src fd00:976a:14fe:30::5 dst 2607:fc20:571f:9bab:ac39:bec2:64e7:407c proto esp spi 1585477955 mode transport enc "cipher_null" "" auth-trunc "hmac(md5)" "0x0b5b41a3d6efb3a65ae176833145ee86" 96 reqid 2415771697
[2025-06-29 7:14:32](Utils): [DEBUG] ip xfrm policy add src 2607:fc20:571f:9bab:ac39:bec2:64e7:407c dst fd00:976a:14fe:30::5 dir out tmpl src 2607:fc20:571f:9bab:ac39:bec2:64e7:407c dst fd00:976a:14fe:30::5 proto esp spi 2552538371 mode transport reqid 2415771697 mark 2684207153
[2025-06-29 7:14:32](Utils): [DEBUG] ip xfrm state add src 2607:fc20:571f:9bab:ac39:bec2:64e7:407c dst fd00:976a:14fe:30::5 proto esp spi 2552538370 mode transport enc "cipher_null" "" auth-trunc "hmac(md5)" "0x0b5b41a3d6efb3a65ae176833145ee86" 96 reqid 2415771697
[2025-06-29 7:14:32](Utils): [DEBUG] ip xfrm state add src fd00:976a:14fe:30::5 dst 2607:fc20:571f:9bab:ac39:bec2:64e7:407c proto esp spi 532441638 mode transport enc "cipher_null" "" auth-trunc "hmac(md5)" "0x0b5b41a3d6efb3a65ae176833145ee86" 96 reqid 2415771697
[2025-06-29 7:14:32](Utils): [DEBUG] ip xfrm policy add src 2607:fc20:571f:9bab:ac39:bec2:64e7:407c dst fd00:976a:14fe:30::5 dir out tmpl src 2607:fc20:571f:9bab:ac39:bec2:64e7:407c dst fd00:976a:14fe:30::5 proto esp spi 2552538370 mode transport reqid 2415771697 mark 2952642609
[2025-06-29 7:14:32](IMSSocketPool): [SUCC] Server listening on: [2607:fc20:571f:9bab:ac39:bec2:64e7:407c]:47654
[2025-06-29 7:14:32](IMSSocketPool): [DEBUG] Connecting to secured endpoint: [fd00:976a:14fe:30::5]:65529...
[2025-06-29 7:14:33](IMSSocketPool): [SUCC] Connected to secured endpoint: [fd00:976a:14fe:30::5]:65529
[2025-06-29 7:14:33](RegisterSequence): [INFO] Initial REGISTER...
[2025-06-29 7:14:33](RegisterSequence): [SUCC] REGISTER OK!
[2025-06-29 7:14:33](RegisterSequence): [WARN] Client URI changed to: [sip:<EMAIL>]
[2025-06-29 7:14:33](RegisterSequence): [INFO] Initial SUBSCRIBE...
[2025-06-29 7:14:33](RegisterSequence): [SUCC] SUBSCRIBE OK!
[2025-06-29 7:14:33](APIServer): [SUCC] Started on: /tmp/imsclient/310240184649727
[2025-06-29 7:14:33](IMSClient): [DEBUG] Received NOTIFY, simply ACK 200 OK and ignore...