package usim

import (
	"errors"
	"fmt"
)

// TLV represents a Tag-Length-Value object.
type TLV struct {
	Tag      byte
	Value    []byte
	Children []*TLV
}

// ErrInvalidFormat indicates that the input data does not conform to the TLV format.
var ErrInvalidFormat = errors.New("usim: invalid TLV format")

// Find searches for a child TLV with the specified tag.
func (t *TLV) Find(tag byte) *TLV {
	for _, child := range t.Children {
		if child.Tag == tag {
			return child
		}
	}
	return nil
}

// UnmarshalBinary decodes a byte slice into a TLV structure, satisfying the
// encoding.BinaryUnmarshaler interface.
func (t *TLV) UnmarshalBinary(data []byte) error {
	// Call the core unmarshaling function.
	tlv, _, err := unmarshal(data)
	if err != nil {
		return err
	}

	// Assign the parsed fields to the receiver 't'.
	*t = *tlv
	return nil
}

// unmarshal attempts to parse a single TLV from the start of a byte slice.
// It returns the parsed TLV, the number of bytes consumed (n), and any error.
func unmarshal(data []byte) (*TLV, int, error) {
	var offset int

	// 1. Decode Tag
	tag, n, err := decodeTag(data[offset:])
	if err != nil {
		return nil, 0, err
	}
	offset += n

	// 2. Decode Length
	length, n, err := decodeLength(data[offset:])
	if err != nil {
		// Add context to the error.
		return nil, 0, fmt.Errorf("tag[0x%02X]: %w", tag, err)
	}
	offset += n

	// 3. Extract Value
	end := offset + length
	if end > len(data) {
		return nil, 0, fmt.Errorf("tag[0x%02X]: declared length %d exceeds available data", tag, length)
	}
	value := data[offset:end]
	offset = end

	// Create the TLV object.
	tlv := &TLV{
		Tag:   tag,
		Value: value,
	}

	// 4. Attempt to parse the value as a sequence of children.
	if len(value) > 0 {
		// If parsing children succeeds, populate the Children field.
		// Otherwise, the error is ignored, and the value is treated as opaque data.
		if children, err := unmarshalChildren(value); err == nil {
			tlv.Children = children
		}
	}

	return tlv, offset, nil
}

// unmarshalChildren attempts to parse a byte slice as a complete sequence of TLV objects.
func unmarshalChildren(data []byte) ([]*TLV, error) {
	var children []*TLV
	var offset int
	for offset < len(data) {
		child, n, err := unmarshal(data[offset:])
		if err != nil {
			// If we fail at any point, the data is not a valid sequence of children.
			return nil, err
		}
		children = append(children, child)
		offset += n
	}
	return children, nil
}

// decodeTag decodes the tag from the beginning of the slice.
func decodeTag(data []byte) (tag byte, n int, err error) {
	if len(data) < 1 {
		return 0, 0, fmt.Errorf("%w: missing tag", ErrInvalidFormat)
	}
	return data[0], 1, nil
}

// decodeLength decodes the length from the beginning of the slice.
func decodeLength(data []byte) (length int, n int, err error) {
	if len(data) < 1 {
		return 0, 0, fmt.Errorf("%w: missing length", ErrInvalidFormat)
	}

	lenByte := data[0]
	if lenByte < 0x80 {
		// Short form (length is 0-127).
		return int(lenByte), 1, nil
	}

	// Long form.
	numLenBytes := int(lenByte & 0x7F)
	if numLenBytes == 0 {
		return 0, 0, fmt.Errorf("%w: indefinite length form (0x80) is not supported", ErrInvalidFormat)
	}

	if 1+numLenBytes > len(data) {
		return 0, 0, fmt.Errorf("%w: not enough bytes for long-form length", ErrInvalidFormat)
	}

	var val uint64
	lenBytes := data[1 : 1+numLenBytes]
	for _, b := range lenBytes {
		val = (val << 8) | uint64(b)
	}

	if val > uint64(^uint(0)>>1) {
		return 0, 0, fmt.Errorf("%w: length %d exceeds max int size", ErrInvalidFormat, val)
	}
	return int(val), 1 + numLenBytes, nil
}
