package usim

import (
	"errors"
	"fmt"

	"github.com/damonto/vowifi/apdu"
)

func (u *USIM) Auth(rand, autn []byte) (result []byte, ik []byte, ck []byte, err error) {
	if len(rand) != 16 || len(autn) != 16 {
		return nil, nil, nil, errors.New("the length of rand and autn must be 32 hex characters (16 bytes)")
	}
	// Tag 0x10 + RAND (16 bytes) + Tag 0x10 + AUTN (16 bytes)
	data := append([]byte{}, 0x10) // RAND tag
	data = append(data, rand...)
	data = append(data, 0x10) // AUTN tag
	data = append(data, autn...)

	request := apdu.Request{
		CLA: 0x00, INS: 0x88,
		P1: 0x00, P2: 0x81, // 3G context
		Data: data,
	}
	response, err := u.transmitter.Transmit(request.APDU())
	if err != nil {
		return nil, nil, nil, fmt.Errorf("APDU command failed: %w", err)
	}
	if response[0] != 0xDB {
		return nil, nil, nil, fmt.Errorf("invalid response tag: expected 0xDB, got 0x%02X", response[0])
	}
	rn := int(response[1])
	if rn < 4 || rn > 16 {
		return nil, nil, nil, fmt.Errorf("invalid RES length: %d", rn)
	}
	return response[2 : 2+rn], response[2+rn : 2+rn+16], response[2+rn+16 : 2+rn+32], nil
}
