package usim

import (
	"fmt"
)

func (u *USIM) GetIMSI() (IMSI, error) {
	if _, err := u.Select(SelectName, u.AID); err != nil {
		return nil, err
	}
	fci, err := u.Select(SelectID, EF_IMSI)
	if err != nil {
		return nil, err
	}
	if !fci.Struct.IsTransparent() {
		return nil, fmt.Errorf("uncommon EF_IMSI struct: %02X", fci.Struct)
	}
	data, err := u.ReadBinary(0, fci.Size)
	if err != nil {
		return nil, err
	}
	return IMSI(data[1 : data[0]+1]), nil
}

func (i IMSI) MCC() string {
	return i.String()[0:3]
}

func (i IMSI) MNC() string {
	s := i.String()
	if s[3] == '0' || s[3] == '1' {
		return "0" + s[3:5]
	}
	return s[3:6]
}
