package usim

import (
	"errors"
	"fmt"
)

func (u *USIM) GetAID() ([]byte, error) {
	if _, err := u.Select(SelectID, MF_ID); err != nil {
		return nil, err
	}
	fci, err := u.Select(SelectID, EF_DIR_ID)
	if err != nil {
		return nil, err
	}
	if !fci.Struct.IsLinearFixed() {
		return nil, fmt.Errorf("uncommon EF_DIR_ID struct: %02X", fci.Struct)
	}
	for i := 1; i <= int(fci.RecordCount); i++ {
		data, err := u.ReadRecord(uint8(i), uint8(fci.RecordSize), false)
		if err != nil {
			return nil, err
		}
		records := new(EFDIRRecords)
		if err := records.UnmarshalBinary(data); err != nil {
			return nil, err
		}
		for _, record := range *records {
			if record.Label == "USIM" {
				return record.AID, nil
			}
		}
	}
	return nil, errors.New("USIM not found")
}
