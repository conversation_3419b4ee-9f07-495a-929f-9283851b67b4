package usim

import (
	"errors"
	"fmt"
	"strings"
)

func (u *USIM) GetSMSC() (string, error) {
	if _, err := u.Select(SelectName, u.AID); err != nil {
		return "", err
	}
	fci, err := u.Select(SelectID, EF_SMSP)
	if err != nil {
		return "", err
	}
	if !fci.Struct.IsLinearFixed() {
		return "", fmt.Errorf("uncommon EF_SMSP struct: %02X", fci.Struct)
	}
	offset := fci.RecordSize - 28
	for i := 1; i <= int(fci.RecordCount); i++ {
		data, err := u.ReadRecord(uint8(i), uint8(fci.RecordSize), false)
		if err != nil {
			return "", err
		}
		data = data[offset+13 : offset+13+12]
		number := Number(data[2 : data[0]+1]).String()
		if strings.Trim(number, "f") != "" {
			if data[1] == 0x91 {
				return "+" + number, nil
			}
			return number, nil
		}
	}
	return "", errors.New("SMSC not found")
}
