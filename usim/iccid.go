package usim

import "fmt"

func (u *USIM) GetICCID() (ICCID, error) {
	if _, err := u.Select(SelectName, u.AID); err != nil {
		return nil, err
	}
	fci, err := u.Select(SelectPath, EF_ICCID_PATH)
	if err != nil {
		return nil, err
	}
	if !fci.Struct.IsTransparent() {
		return nil, fmt.Errorf("uncommon EF_ICCID_PATH struct: %02X", fci.Struct)
	}
	data, err := u.ReadBinary(0, fci.Size)
	if err != nil {
		return nil, err
	}
	return ICCID(data), nil
}
