package usim

import (
	"fmt"
	"slices"
)

type EFDIRRecord struct {
	AID   []byte
	Label string
}

type EFDIRRecords []*EFDIRRecord

func (r *EFDIRRecords) UnmarshalBinary(data []byte) error {
	var tlv TLV
	if err := tlv.UnmarshalBinary(data); err != nil {
		return err
	}
	if tlv.Tag != TagRecord {
		return fmt.Errorf("%w: unexpected tag %02X", ErrInvalidFormat, tlv.Tag)
	}
	var records EFDIRRecords
	for child := range slices.Chunk(tlv.Children, 2) {
		record := EFDIRRecord{AID: child[0].Value}
		if len(child) == 2 {
			record.Label = string(child[1].Value)
		}
		records = append(records, &record)
	}
	*r = records
	return nil
}
