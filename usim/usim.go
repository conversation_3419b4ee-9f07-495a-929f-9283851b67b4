package usim

import (
	"log/slog"

	"github.com/damonto/vowifi/apdu"
	"github.com/damonto/vowifi/driver"
)

type USIM struct {
	logger      *slog.Logger
	AID         []byte
	transmitter driver.Transmitter
}

func New(transmitter driver.Transmitter, logger *slog.Logger) (*USIM, error) {
	u := USIM{transmitter: transmitter, logger: logger}
	var err error
	if u.AID, err = u.GetAID(); err != nil {
		return nil, err
	}
	return &u, nil
}

func (u *USIM) RunAPDU(request apdu.Request) ([]byte, error) {
	response, err := u.transmitter.Transmit(request.APDU())
	if err != nil {
		return nil, err
	}
	return response, nil
}

func (u *USIM) Select(P1 byte, file []byte) (*FCI, error) {
	request := apdu.Request{CLA: 0x00, INS: 0xA4, P1: P1, P2: 0x04, Data: file}
	response, err := u.RunAPDU(request)
	if err != nil {
		return nil, err
	}
	fci := new(FCI)
	if err := fci.UnmarshalBinary(response); err != nil {
		return nil, err
	}
	return fci, nil
}

func (u *USIM) ReadBinary(offset uint16, le uint8) ([]byte, error) {
	request := apdu.Request{CLA: 0x00, INS: 0xB0, P1: byte((offset >> 8) & 0xFF), P2: byte(offset & 0xFF), Le: &le}
	return u.RunAPDU(request)
}

func (u *USIM) ReadRecord(offset uint8, le uint8, abs bool) ([]byte, error) {
	request := apdu.Request{CLA: 0x00, INS: 0xB2, P1: offset, P2: 0x04, Le: &le}
	if abs {
		request.P2 = 0x04
	}
	return u.RunAPDU(request)
}
