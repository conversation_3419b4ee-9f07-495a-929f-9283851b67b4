package usim

import "fmt"

type FileStruct uint8

const (
	FileStructTransparent FileStruct = 0x41
	FileStructLinearFixed FileStruct = 0x42
)

func (fs FileStruct) IsTransparent() bool {
	return fs == FileStructTransparent
}

func (fs FileStruct) IsLinearFixed() bool {
	return fs == FileStructLinearFixed
}

type FCI struct {
	Struct      FileStruct
	Type        uint8
	RecordSize  uint16
	RecordCount uint8
	Size        uint8
}

func (f *FCI) UnmarshalBinary(data []byte) error {
	var tlv TLV
	if err := tlv.UnmarshalBinary(data); err != nil {
		return err
	}
	if tlv.Tag != TagFCI {
		return fmt.Errorf("%w: unexpected tag %02X", ErrInvalidFormat, tlv.Tag)
	}
	var fd *TLV
	if fd = tlv.Find(TagFileDescriptor); fd == nil {
		return fmt.Errorf("%w: missing file descriptor", ErrInvalidFormat)
	}
	if len(fd.Value) < 2 {
		return fmt.Errorf("%w: unexpected file descriptor length", ErrInvalidFormat)
	}
	f.Struct = FileStruct(fd.Value[0])
	f.Type = fd.Value[1]
	if len(fd.Value) == 5 {
		f.RecordSize = uint16(fd.Value[2])<<8 | uint16(fd.Value[3])
		f.RecordCount = fd.Value[4]
	}
	len := tlv.Find(TagFileLength)
	if len != nil {
		f.Size = uint8(len.Value[1])
	}
	return nil
}
