package usim

import (
	"encoding/hex"
	"errors"
	"strings"
)

type Number []byte

func NewNumber(number string) (Number, error) {
	return binaryCodedDecimalEncode[Number](number)
}

func (number Number) String() string {
	return binaryCodedDecimalDecode(number)
}

type IMSI []byte

func NewIMSI(imsi string) (IMSI, error) {
	return binaryCodedDecimalEncode[IMSI](imsi)
}

func (imsi IMSI) String() string {
	return binaryCodedDecimalDecode(imsi)[1:]
}

type ICCID []byte

func NewICCID(iccid string) (ICCID, error) {
	return binaryCodedDecimalEncode[ICCID](iccid)
}

func (id ICCID) String() string {
	return binaryCodedDecimalDecode(id)
}

type IMEI []byte

func NewIMEI(imei string) (IMEI, error) {
	return binaryCodedDecimalEncode[IMEI](imei)
}

func (imei IMEI) String() string {
	return binaryCodedDecimalDecode(imei)
}

func binaryCodedDecimalEncode[T ~[]byte](value string) (T, error) {
	for _, r := range value {
		if (r < '0' || r > '9') && (r < 'A' || r > 'F') && (r < 'a' || r > 'f') {
			return nil, errors.New("invalid value")
		}
	}
	if len(value)%2 != 0 {
		value += "F"
	}
	id, _ := hex.DecodeString(value)
	for index := range id {
		id[index] = (id[index]>>4)&0x0F | (id[index]<<4)&0xF0
	}
	return T(id), nil
}

func binaryCodedDecimalDecode(value []byte) string {
	id := make([]byte, len(value))
	var index int
	for index = range value {
		id[index] = (value[index]>>4)&0x0F | (value[index]<<4)&0xF0
	}
	points := hex.EncodeToString(id)
	if index := strings.LastIndexByte(points, 'f'); index != -1 {
		points = points[:index]
	}
	return points
}
