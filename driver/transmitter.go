package driver

import (
	"fmt"
	"io"
	"log/slog"

	"github.com/damonto/vowifi/apdu"
)

type Transmitter interface {
	Transmit(command []byte) ([]byte, error)
	Close() error
}

type transmitter struct {
	usim   io.ReadWriteCloser
	logger *slog.Logger
}

func NewTransmitter(logger *slog.Logger, usim apdu.USIM) (Transmitter, error) {
	t, err := apdu.NewTransmitter(usim)
	if err != nil {
		return nil, err
	}
	return &transmitter{usim: t, logger: logger}, nil
}

func (t *transmitter) Transmit(command []byte) ([]byte, error) {
	t.logger.Debug("[APDU] sending", "command", fmt.Sprintf("%X", command))
	if _, err := t.usim.Write(command); err != nil {
		return nil, err
	}
	bs, err := io.ReadAll(t.usim)
	if err != nil {
		return nil, err
	}
	t.logger.Debug("[APDU] received", "response", fmt.Sprintf("%X", bs))
	return bs, err
}

func (t *transmitter) Close() error {
	return t.usim.Close()
}
