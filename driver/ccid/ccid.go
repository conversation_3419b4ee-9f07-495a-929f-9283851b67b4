package ccid

import (
	"errors"

	"github.com/ElMostafaIdrassi/goscard"
	"github.com/damonto/vowifi/apdu"
)

type CCID interface {
	apdu.USIM
	ListReaders() ([]string, error)
	SetReader(reader string)
}

type CCIDReader struct {
	context goscard.Context
	card    goscard.Card
	reader  string
}

func New() (CCID, error) {
	if err := goscard.Initialize(goscard.NewDefaultLogger(goscard.LogLevelNone)); err != nil {
		return nil, err
	}
	context, _, err := goscard.NewContext(goscard.SCardScopeSystem, nil, nil)
	if err != nil {
		return nil, err
	}
	ccid := &CCIDReader{context: context}
	return ccid, nil
}

func (c *CCIDReader) ListReaders() ([]string, error) {
	readers, _, err := c.context.ListReaders(nil)
	if err != nil {
		return nil, err
	}
	if len(readers) == 0 {
		return nil, errors.New("no readers found")
	}
	return readers, nil
}

func (c *CCIDReader) SetReader(reader string) {
	c.reader = reader
}

func (c *CCIDReader) Connect() error {
	var err error
	c.card, _, err = c.context.Connect(c.reader, goscard.SCardShareExclusive, goscard.SCardProtocolT0)
	return err
}

func (c *CCIDReader) Close() error {
	defer goscard.Finalize()
	if _, err := c.card.Disconnect(goscard.SCardLeaveCard); err != nil {
		return err
	}
	if _, err := c.context.Release(); err != nil {
		return err
	}
	return nil
}

func (c *CCIDReader) Transmit(command []byte) ([]byte, error) {
	r, _, err := c.card.Transmit(&goscard.SCardIoRequestT0, command, nil)
	return r, err
}
