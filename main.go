package main

import (
	"log/slog"

	"github.com/damonto/vowifi/driver"
	"github.com/damonto/vowifi/driver/ccid"
	"github.com/damonto/vowifi/usim"
)

func main() {
	// Set up logging
	slog.SetLogLoggerLevel(slog.LevelDebug)
	logger := slog.Default()

	// Initialize CCID driver
	d, err := ccid.New()
	if err != nil {
		logger.Error("Failed to create CCID driver", "error", err)
		panic(err)
	}

	// Set the card reader (adjust this to match your reader)
	d.Set<PERSON>eader("Alcor Link AK9563 00 00")

	// Create transmitter
	transmitter, err := driver.NewTransmitter(logger, d)
	if err != nil {
		logger.Error("Failed to create transmitter", "error", err)
		panic(err)
	}
	defer transmitter.Close()

	// Initialize USIM
	usimCard, err := usim.New(transmitter, logger)
	if err != nil {
		logger.Error("Failed to initialize USIM", "error", err)
		panic(err)
	}

	// Get USIM information
	imsi, err := usimCard.GetIMSI()
	if err != nil {
		logger.Error("Failed to get IMSI", "error", err)
		panic(err)
	}

	iccid, err := usimCard.GetICCID()
	if err != nil {
		logger.Error("Failed to get ICCID", "error", err)
		panic(err)
	}

	logger.Info("USIM Information",
		"imsi", imsi.String(),
		"mcc", imsi.MCC(),
		"mnc", imsi.MNC(),
		"iccid", iccid.String())

	// Create IMEI (using the same IMEI as PHP implementation for consistency)
	imei, err := usim.NewIMEI("356656421305276")
	if err != nil {
		logger.Error("Failed to create IMEI", "error", err)
		panic(err)
	}

	// Run VoWiFi connection
}
