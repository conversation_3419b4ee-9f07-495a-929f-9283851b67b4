package main

import (
	"context"
	"log/slog"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/damonto/vowifi/driver"
	"github.com/damonto/vowifi/driver/ccid"
	"github.com/damonto/vowifi/swuclient"
	"github.com/damonto/vowifi/usim"
)

func main() {
	// Set up logging
	slog.SetLogLoggerLevel(slog.LevelDebug)
	logger := slog.Default()

	// Initialize CCID driver
	d, err := ccid.New()
	if err != nil {
		logger.Error("Failed to create CCID driver", "error", err)
		panic(err)
	}

	// Set the card reader (adjust this to match your reader)
	d.<PERSON><PERSON>eader("Alcor Link AK9563 00 00")

	// Create transmitter
	transmitter, err := driver.NewTransmitter(logger, d)
	if err != nil {
		logger.Error("Failed to create transmitter", "error", err)
		panic(err)
	}
	defer transmitter.Close()

	// Initialize USIM
	usimCard, err := usim.New(transmitter, logger)
	if err != nil {
		logger.Error("Failed to initialize USIM", "error", err)
		panic(err)
	}

	// Get USIM information
	imsi, err := usimCard.GetIMSI()
	if err != nil {
		logger.Error("Failed to get IMSI", "error", err)
		panic(err)
	}

	iccid, err := usimCard.GetICCID()
	if err != nil {
		logger.Error("Failed to get ICCID", "error", err)
		panic(err)
	}

	logger.Info("USIM Information",
		"imsi", imsi.String(),
		"mcc", imsi.MCC(),
		"mnc", imsi.MNC(),
		"iccid", iccid.String())

	// Create IMEI (using the same IMEI as PHP implementation for consistency)
	imei, err := usim.NewIMEI("356656421305276")
	if err != nil {
		logger.Error("Failed to create IMEI", "error", err)
		panic(err)
	}

	// Run VoWiFi connection
	runVoWiFiConnection(logger, usimCard, &imei)
}

func runVoWiFiConnection(logger *slog.Logger, usimCard *usim.USIM, imei *usim.IMEI) {
	logger.Info("Starting VoWiFi connection process")

	// Create context for the client
	ctx := context.Background()

	// Create SWu client options
	opts := &swuclient.Option{
		USIM: usimCard,
		IMEI: imei,
		// Address will be auto-generated from USIM MCC/MNC
		// NAI will be auto-generated from USIM IMSI
	}

	// Create SWu client
	client, err := swuclient.NewClient(&ctx, opts, logger)
	if err != nil {
		logger.Error("Failed to create SWu client", "error", err)
		return
	}

	// Display connection information
	connInfo := client.GetConnectionInfo()
	logger.Info("SWu Client Configuration",
		"identity", connInfo["identity"],
		"epdg_address", connInfo["epdg_address"])

	// Display USIM information
	usimInfo, err := client.GetUSIMInfo()
	if err != nil {
		logger.Error("Failed to get USIM info", "error", err)
		return
	}

	logger.Info("USIM Configuration",
		"imsi", usimInfo["imsi"],
		"mcc", usimInfo["mcc"],
		"mnc", usimInfo["mnc"],
		"nai", usimInfo["nai"],
		"imei", usimInfo["imei"])

	// Test USIM authentication
	if err := client.TestUSIMAuth(); err != nil {
		logger.Error("USIM authentication test failed", "error", err)
		return
	}

	// Set up signal handling for graceful shutdown
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// Start connection in a goroutine
	go func() {
		logger.Info("Attempting to establish SWu IKEv2 connection...")

		if err := client.Connect(); err != nil {
			logger.Error("Failed to establish SWu connection", "error", err)
			return
		}

		logger.Info("SWu IKEv2 connection established successfully!")

		// Keep connection alive and monitor status
		ticker := time.NewTicker(30 * time.Second)
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				if client.IsConnected() {
					logger.Info("SWu connection status check",
						"connected", client.IsConnected(),
						"tunnel_active", client.IsTunnelActive(),
						"state", client.GetState())
				} else {
					logger.Warn("SWu connection lost, attempting to reconnect...")
					if err := client.Connect(); err != nil {
						logger.Error("Reconnection failed", "error", err)
					}
				}
			case <-ctx.Done():
				return
			}
		}
	}()

	// Wait for shutdown signal
	<-sigChan
	logger.Info("Shutdown signal received, disconnecting...")

	// Graceful shutdown
	if err := client.Disconnect(); err != nil {
		logger.Error("Error during disconnect", "error", err)
	}

	logger.Info("VoWiFi client shutdown complete")
}
