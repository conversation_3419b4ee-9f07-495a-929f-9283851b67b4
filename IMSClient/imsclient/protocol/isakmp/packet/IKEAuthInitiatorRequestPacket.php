<?php

namespace imsclient\protocol\isakmp\packet;

use imsclient\protocol\isakmp\CryptoHelper;
use imsclient\protocol\isakmp\payload\Attribute;
use imsclient\protocol\isakmp\payload\ConfigurationPayload;
use imsclient\protocol\isakmp\payload\DeviceIdentityNotifyPayload;
use imsclient\protocol\isakmp\payload\IdentificationPayload;
use imsclient\protocol\isakmp\payload\InitiatorTrafficSelectorPayload;
use imsclient\protocol\isakmp\payload\NotifyPayload;
use imsclient\protocol\isakmp\payload\ProposalPayload;
use imsclient\protocol\isakmp\payload\ResponderIdentificationPayload;
use imsclient\protocol\isakmp\payload\ResponderTrafficSelectorPayload;
use imsclient\protocol\isakmp\payload\SecurityAssociationPayload;
use imsclient\protocol\isakmp\payload\TrafficSelector;
use imsclient\log\Logger;
use imsclient\protocol\isakmp\payload\TransformPayload;

class IKEAuthInitiatorRequestPacket extends EncryptedPacket
{
    protected static $_exchangetype = self::EXCHANGE_TYPE_IKE_AUTH;

    protected function default()
    {
        // ESP SA Here
        $sa = new SecurityAssociationPayload([
            new ProposalPayload(ProposalPayload::PROTOCOL_ID_ESP, pack('N', $this->transaction->esp_spi_initiator), [
                new TransformPayload(TransformPayload::TYPE_ENCR, CryptoHelper::ENCR_AES_CBC, [
                    new Attribute(Attribute::FORMAT_TV, TransformPayload::ATTR_KEY_LENGTH, 128)
                ]),
                new TransformPayload(TransformPayload::TYPE_INTEG, CryptoHelper::AUTH_HMAC_MD5_96),
                new TransformPayload(TransformPayload::TYPE_ESN, 0)
            ]),
            new ProposalPayload(ProposalPayload::PROTOCOL_ID_ESP, pack('N', $this->transaction->esp_spi_initiator), [
                new TransformPayload(TransformPayload::TYPE_ENCR, CryptoHelper::ENCR_AES_CBC, [
                    new Attribute(Attribute::FORMAT_TV, TransformPayload::ATTR_KEY_LENGTH, 256)
                ]),
                new TransformPayload(TransformPayload::TYPE_INTEG, CryptoHelper::AUTH_HMAC_MD5_96),
                new TransformPayload(TransformPayload::TYPE_ESN, 0)
            ]),
            new ProposalPayload(ProposalPayload::PROTOCOL_ID_ESP, pack('N', $this->transaction->esp_spi_initiator), [
                new TransformPayload(TransformPayload::TYPE_ENCR, CryptoHelper::ENCR_AES_CBC, [
                    new Attribute(Attribute::FORMAT_TV, TransformPayload::ATTR_KEY_LENGTH, 128)
                ]),
                new TransformPayload(TransformPayload::TYPE_INTEG, CryptoHelper::AUTH_HMAC_SHA1_96),
                new TransformPayload(TransformPayload::TYPE_ESN, 0)
            ]),
            new ProposalPayload(ProposalPayload::PROTOCOL_ID_ESP, pack('N', $this->transaction->esp_spi_initiator), [
                new TransformPayload(TransformPayload::TYPE_ENCR, CryptoHelper::ENCR_AES_CBC, [
                    new Attribute(Attribute::FORMAT_TV, TransformPayload::ATTR_KEY_LENGTH, 256)
                ]),
                new TransformPayload(TransformPayload::TYPE_INTEG, CryptoHelper::AUTH_HMAC_SHA1_96),
                new TransformPayload(TransformPayload::TYPE_ESN, 0)
            ]),
            new ProposalPayload(ProposalPayload::PROTOCOL_ID_ESP, pack('N', $this->transaction->esp_spi_initiator), [
                new TransformPayload(TransformPayload::TYPE_ENCR, CryptoHelper::ENCR_AES_CBC, [
                    new Attribute(Attribute::FORMAT_TV, TransformPayload::ATTR_KEY_LENGTH, 128)
                ]),
                new TransformPayload(TransformPayload::TYPE_INTEG, CryptoHelper::AUTH_HMAC_SHA2_256_128),
                new TransformPayload(TransformPayload::TYPE_ESN, 0)
            ]),
            new ProposalPayload(ProposalPayload::PROTOCOL_ID_ESP, pack('N', $this->transaction->esp_spi_initiator), [
                new TransformPayload(TransformPayload::TYPE_ENCR, CryptoHelper::ENCR_AES_CBC, [
                    new Attribute(Attribute::FORMAT_TV, TransformPayload::ATTR_KEY_LENGTH, 256)
                ]),
                new TransformPayload(TransformPayload::TYPE_INTEG, CryptoHelper::AUTH_HMAC_SHA2_256_128),
                new TransformPayload(TransformPayload::TYPE_ESN, 0)
            ]),
        ]);

        $iid = $this->transaction->id_initiator;
        $rid = new ResponderIdentificationPayload(IdentificationPayload::TYPE_FQDN, "ims");

        $conf = new ConfigurationPayload(ConfigurationPayload::TYPE_CFG_REQUEST);
        $conf->attribute[] = new Attribute(Attribute::FORMAT_TLV, ConfigurationPayload::ATTR_INTERNAL_IP4_ADDRESS);
        $conf->attribute[] = new Attribute(Attribute::FORMAT_TLV, ConfigurationPayload::ATTR_INTERNAL_IP4_DNS);
        $conf->attribute[] = new Attribute(Attribute::FORMAT_TLV, ConfigurationPayload::ATTR_INTERNAL_IP6_ADDRESS);
        $conf->attribute[] = new Attribute(Attribute::FORMAT_TLV, ConfigurationPayload::ATTR_INTERNAL_IP6_DNS);
        $conf->attribute[] = new Attribute(Attribute::FORMAT_TLV, ConfigurationPayload::ATTR_P_CSCF_IP4_ADDRESS);
        $conf->attribute[] = new Attribute(Attribute::FORMAT_TLV, ConfigurationPayload::ATTR_P_CSCF_IP6_ADDRESS);

        $its = new InitiatorTrafficSelectorPayload();
        $its->selector[] = new TrafficSelector(TrafficSelector::TS_IPV4_ADDR_RANGE, 0, "\x00\x00\xff\xff" . str_repeat("\x00", 4) . str_repeat("\xff", 4));
        $its->selector[] = new TrafficSelector(TrafficSelector::TS_IPV6_ADDR_RANGE, 0, "\x00\x00\xff\xff" . str_repeat("\x00", 16) . str_repeat("\xff", 16));

        $rts = new ResponderTrafficSelectorPayload();
        $rts->selector[] = new TrafficSelector(TrafficSelector::TS_IPV4_ADDR_RANGE, 0, "\x00\x00\xff\xff" . str_repeat("\x00", 4) . str_repeat("\xff", 4));
        $rts->selector[] = new TrafficSelector(TrafficSelector::TS_IPV6_ADDR_RANGE, 0, "\x00\x00\xff\xff" . str_repeat("\x00", 16) . str_repeat("\xff", 16));

        $eaponlynotify = new NotifyPayload();
        $eaponlynotify->protocol_id = NotifyPayload::PROTOCOL_ID_NONE;
        $eaponlynotify->notify_type = NotifyPayload::TYPE_EAP_ONLY_AUTHENTICATION;

        $imeisvnotify = new DeviceIdentityNotifyPayload();
        $imeisvnotify->imei = $this->transaction->imei;

        // Debug: Log payload construction order
        Logger::debug("[PHP_DEBUG] Adding payloads in order:");
        Logger::debug("[PHP_DEBUG] 1. Initiator ID payload - NAI: " . $this->transaction->id_initiator->data);
        $this->payload[] = $iid;

        Logger::debug("[PHP_DEBUG] 2. Responder ID payload - NAI: ims");
        $this->payload[] = $rid;

        Logger::debug("[PHP_DEBUG] 3. Configuration Request payload");
        $this->payload[] = $conf;

        Logger::debug("[PHP_DEBUG] 4. ESP Security Association payload with " . count($sa->proposal) . " proposals");
        $this->payload[] = $sa;

        Logger::debug("[PHP_DEBUG] 5. Initiator Traffic Selector payload");
        $this->payload[] = $its;

        Logger::debug("[PHP_DEBUG] 6. Responder Traffic Selector payload");
        $this->payload[] = $rts;

        Logger::debug("[PHP_DEBUG] 7. EAP-Only Authentication Notify payload");
        $this->payload[] = $eaponlynotify;

        Logger::debug("[PHP_DEBUG] 8. Device Identity Notify payload - IMEI: " . $this->transaction->imei);
        $this->payload[] = $imeisvnotify;

        Logger::debug("[PHP_DEBUG] Total payloads added: " . count($this->payload));
    }
}
