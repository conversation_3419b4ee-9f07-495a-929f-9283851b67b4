<?php

namespace imsclient\protocol\isakmp\packet;

use imsclient\protocol\isakmp\payload\AuthenticationPayload;
use imsclient\log\Logger;

class IKEAuthRequest extends EncryptedPacket
{
    protected static $_exchangetype = self::EXCHANGE_TYPE_IKE_AUTH;

    public function default()
    {
        $crypto = $this->transaction->crypto;

        // Debug: Log ID payload raw data
        $id_raw_payload = $this->transaction->id_initiator->getRawPayload();
        Logger::debug("[PHP_DEBUG] ID payload raw data: " . bin2hex($id_raw_payload) . " (length: " . strlen($id_raw_payload) . ")");

        // Debug: Log SK_PI key
        Logger::debug("[PHP_DEBUG] SK_PI key: " . bin2hex($this->transaction->crypto->sk_pi));

        // Step 1: Calculate hash of ID payload using SK_PI
        $hash = $crypto->prfHmac($id_raw_payload, $this->transaction->crypto->sk_pi);
        Logger::debug("[PHP_DEBUG] ID payload hash (step 1): " . bin2hex($hash));

        // Debug: Log packet construction components
        Logger::debug("[PHP_DEBUG] IKE_SA_INIT buffer length: " . strlen($this->transaction->ike_sa_init_pkt_buffer));
        Logger::debug("[PHP_DEBUG] Nonce responder: " . bin2hex($this->transaction->nonce_responder) . " (length: " . strlen($this->transaction->nonce_responder) . ")");

        // Step 2: Construct packet data
        $packet = $this->transaction->ike_sa_init_pkt_buffer . $this->transaction->nonce_responder . $hash;
        Logger::debug("[PHP_DEBUG] Combined packet data length: " . strlen($packet));
        Logger::debug("[PHP_DEBUG] Combined packet data (first 64 bytes): " . bin2hex(substr($packet, 0, 64)));

        // Debug: Log EAP-MSK
        Logger::debug("[PHP_DEBUG] EAP-MSK: " . bin2hex($crypto->eap_msk) . " (length: " . strlen($crypto->eap_msk) . ")");

        // Step 3: Generate temporary key using EAP-MSK
        $tmpkey = $crypto->prfHmac("Key Pad for IKEv2", $crypto->eap_msk);
        Logger::debug("[PHP_DEBUG] Temporary key: " . bin2hex($tmpkey));

        // Step 4: Calculate final authentication hash
        $hash = $crypto->prfHmac($packet, $tmpkey);
        Logger::debug("[PHP_DEBUG] Final authentication hash: " . bin2hex($hash));

        $this->payload[] = new AuthenticationPayload(AuthenticationPayload::METHOD_SHARED_KEY_MESSAGE_INTEGRITY_CODE, $hash);
    }
}
